const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const sampleModules = [
  {
    name: 'user-authentication',
    displayName: 'User Authentication',
    version: '2.1.0',
    description: 'Advanced user authentication with multi-factor support',
    author: 'WebVue Team',
    category: 'authentication',
    icon: 'Shield',
    route: '/auth-module',
    status: 'active',
    health: 95,
    size: '2.4 MB',
    dependencies: ['crypto', 'jwt'],
    config: {
      enableMFA: true,
      sessionTimeout: 3600,
      maxLoginAttempts: 5
    },
    isActive: true,
    sortOrder: 1
  },
  {
    name: 'google-calendar',
    displayName: 'Google Calendar Integration',
    version: '1.5.2',
    description: 'Seamless integration with Google Calendar API',
    author: 'Integration Team',
    category: 'integration',
    icon: 'Calendar',
    route: '/calendar',
    status: 'active',
    health: 88,
    size: '1.8 MB',
    dependencies: ['google-auth', 'calendar-api'],
    config: {
      apiKey: '',
      syncInterval: 300
    },
    isActive: true,
    sortOrder: 2
  },
  {
    name: 'analytics-dashboard',
    displayName: 'Analytics Dashboard',
    version: '3.0.1',
    description: 'Comprehensive analytics and reporting dashboard',
    author: 'Analytics Corp',
    category: 'analytics',
    icon: 'BarChart3',
    route: '/analytics',
    status: 'inactive',
    health: 72,
    size: '5.2 MB',
    dependencies: ['charts', 'data-processing'],
    config: {
      refreshInterval: 60,
      maxDataPoints: 1000
    },
    isActive: false,
    sortOrder: 3
  },
  {
    name: 'security-monitor',
    displayName: 'Security Monitor',
    version: '1.2.3',
    description: 'Real-time security monitoring and threat detection',
    author: 'SecureVue',
    category: 'security',
    icon: 'Shield',
    route: '/security',
    status: 'error',
    health: 45,
    size: '3.1 MB',
    dependencies: ['security-lib', 'monitoring'],
    config: {
      alertThreshold: 80,
      scanInterval: 120
    },
    isActive: false,
    sortOrder: 4
  },
  {
    name: 'zoom-integration',
    displayName: 'Zoom Integration',
    version: '2.0.0',
    description: 'Video conferencing integration with Zoom API',
    author: 'Communication Team',
    category: 'integration',
    icon: 'Video',
    route: '/zoom',
    status: 'active',
    health: 92,
    size: '2.7 MB',
    dependencies: ['zoom-sdk', 'video-utils'],
    config: {
      maxParticipants: 100,
      recordingEnabled: true
    },
    isActive: true,
    sortOrder: 5
  },
  {
    name: 'audit-logger',
    displayName: 'Audit Logger',
    version: '1.8.0',
    description: 'Comprehensive audit logging and compliance tracking',
    author: 'Compliance Team',
    category: 'security',
    icon: 'FileText',
    route: '/audit',
    status: 'active',
    health: 98,
    size: '1.5 MB',
    dependencies: ['logging', 'encryption'],
    config: {
      logLevel: 'info',
      retentionDays: 365
    },
    isActive: true,
    sortOrder: 6
  }
];

async function seedModules() {
  try {
    console.log('🌱 Seeding modules...');

    // Clear existing modules
    await prisma.module.deleteMany({});
    console.log('✅ Cleared existing modules');

    // Insert sample modules
    for (const moduleData of sampleModules) {
      await prisma.module.create({
        data: moduleData
      });
      console.log(`✅ Created module: ${moduleData.displayName}`);
    }

    console.log('🎉 Module seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding modules:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  seedModules()
    .then(() => {
      console.log('Module seeding finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Module seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedModules };
