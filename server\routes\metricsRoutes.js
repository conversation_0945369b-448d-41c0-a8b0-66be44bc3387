/**
 * Performance Metrics Routes
 * مسارات مقاييس الأداء
 */

const express = require('express');
const { requireUser } = require('./middleware/auth');
const { requireAdmin } = require('./middleware/roleCheck');
const { getPerformanceMetrics, resetPerformanceMetrics } = require('./middleware/performanceMonitor');

const router = express.Router();

/**
 * @swagger
 * /api/metrics/performance:
 *   get:
 *     summary: Get system performance metrics
 *     tags: [Metrics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Performance metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     requests:
 *                       type: object
 *                     authentication:
 *                       type: object
 *                     database:
 *                       type: object
 *                     memory:
 *                       type: object
 *                     errors:
 *                       type: object
 *                     uptime:
 *                       type: number
 *                     timestamp:
 *                       type: string
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get('/performance',
  requireUser,
  requireAdmin,
  async (req, res) => {
    try {
      const metrics = getPerformanceMetrics();
      
      res.json({
        success: true,
        data: metrics
      });
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve performance metrics',
        message: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/metrics/health:
 *   get:
 *     summary: Get system health status
 *     tags: [Metrics]
 *     responses:
 *       200:
 *         description: System health status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [healthy, degraded, unhealthy]
 *                 timestamp:
 *                   type: string
 *                 uptime:
 *                   type: number
 *                 memory:
 *                   type: object
 *                 database:
 *                   type: object
 */
router.get('/health', async (req, res) => {
  try {
    const metrics = getPerformanceMetrics();
    const { prisma } = require('../config/prisma');
    
    // Check database connectivity
    let dbStatus = 'healthy';
    let dbResponseTime = 0;
    
    try {
      const start = Date.now();
      await prisma.$queryRaw`SELECT 1`;
      dbResponseTime = Date.now() - start;
      
      if (dbResponseTime > 100) {
        dbStatus = 'degraded';
      }
    } catch (error) {
      dbStatus = 'unhealthy';
    }
    
    // Determine overall system status
    let systemStatus = 'healthy';
    
    if (metrics.memory.heapUsed > 500 || dbStatus === 'unhealthy') {
      systemStatus = 'unhealthy';
    } else if (metrics.requests.averageResponseTime > 500 || dbStatus === 'degraded') {
      systemStatus = 'degraded';
    }
    
    res.json({
      status: systemStatus,
      timestamp: new Date().toISOString(),
      uptime: Math.round(process.uptime()),
      memory: {
        heapUsed: metrics.memory.heapUsed,
        heapTotal: metrics.memory.heapTotal,
        usage: Math.round((metrics.memory.heapUsed / metrics.memory.heapTotal) * 100)
      },
      database: {
        status: dbStatus,
        responseTime: dbResponseTime
      },
      requests: {
        total: metrics.requests.total,
        averageResponseTime: Math.round(metrics.requests.averageResponseTime),
        errorRate: metrics.requests.total > 0 ? 
          Math.round((metrics.requests.failed / metrics.requests.total) * 100) : 0
      }
    });
  } catch (error) {
    console.error('Error getting health status:', error);
    res.status(500).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: 'Failed to retrieve health status'
    });
  }
});

/**
 * @swagger
 * /api/metrics/reset:
 *   post:
 *     summary: Reset performance metrics
 *     tags: [Metrics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Metrics reset successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.post('/reset',
  requireUser,
  requireAdmin,
  async (req, res) => {
    try {
      resetPerformanceMetrics();
      
      res.json({
        success: true,
        message: 'Performance metrics reset successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error resetting performance metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to reset performance metrics',
        message: error.message
      });
    }
  }
);

/**
 * @swagger
 * /api/metrics/auth:
 *   get:
 *     summary: Get authentication-specific metrics
 *     tags: [Metrics]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Authentication metrics retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get('/auth',
  requireUser,
  requireAdmin,
  async (req, res) => {
    try {
      const metrics = getPerformanceMetrics();
      
      const authMetrics = {
        authentication: metrics.authentication,
        errors: {
          authentication: metrics.errors.authentication,
          total: metrics.errors.total
        },
        performance: {
          averageLoginTime: Math.round(metrics.authentication.averageLoginTime),
          averageVerificationTime: Math.round(metrics.authentication.averageVerificationTime)
        },
        rates: {
          loginSuccessRate: metrics.authentication.logins > 0 ? 
            Math.round(((metrics.authentication.logins - metrics.authentication.loginFailures) / metrics.authentication.logins) * 100) : 100,
          loginFailureRate: metrics.authentication.logins > 0 ? 
            Math.round((metrics.authentication.loginFailures / metrics.authentication.logins) * 100) : 0
        },
        timestamp: new Date().toISOString()
      };
      
      res.json({
        success: true,
        data: authMetrics
      });
    } catch (error) {
      console.error('Error getting authentication metrics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve authentication metrics',
        message: error.message
      });
    }
  }
);

module.exports = router;
