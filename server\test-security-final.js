const axios = require('axios');
const { PrismaClient } = require('@prisma/client');

const BASE_URL = 'http://localhost:3000';
const prisma = new PrismaClient();

async function testSecurityImplementation() {
  try {
    console.log('🛡️ اختبار تطبيق الأمان الجديد...\n');
    
    // تسجيل دخول المدير والمستخدم العادي
    console.log('🔐 تسجيل الدخول...');
    const adminLogin = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'testpassword'
    });
    const adminToken = adminLogin.data.accessToken;
    console.log('✅ تم تسجيل دخول المدير');

    const userLogin = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'testpassword'
    });
    const userToken = userLogin.data.accessToken;
    console.log('✅ تم تسجيل دخول المستخدم العادي\n');
    
    // اختبار 1: وصول المدير للأدوار
    console.log('📋 اختبار 1: وصول المدير للأدوار');
    try {
      const adminRolesResponse = await axios.get(`${BASE_URL}/api/roles`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log(`✅ المدير يمكنه الوصول - تم جلب ${adminRolesResponse.data.length} دور`);
    } catch (error) {
      console.error('❌ المدير لا يمكنه الوصول:', error.response?.data?.message);
    }
    
    // اختبار 2: منع المستخدم العادي من الوصول للأدوار
    console.log('\n👤 اختبار 2: منع المستخدم العادي من الوصول للأدوار');
    try {
      const userRolesResponse = await axios.get(`${BASE_URL}/api/roles`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log('⚠️ المستخدم العادي يمكنه الوصول (هذا خطأ!)');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ تم منع المستخدم العادي بنجاح:', error.response.data.message);
      } else {
        console.error('❌ خطأ غير متوقع:', error.response?.data);
      }
    }
    
    // اختبار 3: منع المستخدم العادي من إنشاء الأدوار
    console.log('\n➕ اختبار 3: منع المستخدم العادي من إنشاء الأدوار');
    try {
      const newRole = {
        name: 'unauthorized_role_' + Date.now(),
        description: 'محاولة إنشاء دور غير مصرح بها',
        permissions: { test: ['read'] }
      };
      
      const createResponse = await axios.post(`${BASE_URL}/api/roles`, newRole, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log('⚠️ المستخدم العادي يمكنه إنشاء الأدوار (هذا خطأ!)');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ تم منع المستخدم العادي من إنشاء الأدوار:', error.response.data.message);
      } else {
        console.error('❌ خطأ غير متوقع:', error.response?.data);
      }
    }
    
    // اختبار 4: المدير يمكنه إنشاء الأدوار
    console.log('\n👑 اختبار 4: المدير يمكنه إنشاء الأدوار');
    try {
      const newRole = {
        name: 'admin_test_role_' + Date.now(),
        description: 'دور اختبار من المدير',
        permissions: { test: ['read', 'write'] }
      };
      
      const createResponse = await axios.post(`${BASE_URL}/api/roles`, newRole, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log('✅ المدير يمكنه إنشاء الأدوار بنجاح');
      
      // حذف الدور المؤقت
      await axios.delete(`${BASE_URL}/api/roles/${createResponse.data.id}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log('✅ تم حذف الدور المؤقت');
      
    } catch (error) {
      console.error('❌ المدير لا يمكنه إنشاء الأدوار:', error.response?.data);
    }
    
    // اختبار 5: منع الوصول بدون token
    console.log('\n🚫 اختبار 5: منع الوصول بدون token');
    try {
      const noTokenResponse = await axios.get(`${BASE_URL}/api/roles`);
      console.log('⚠️ تم السماح بالوصول بدون token (هذا خطأ!)');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ تم منع الوصول بدون token بنجاح');
      } else {
        console.error('❌ خطأ غير متوقع:', error.response?.data);
      }
    }
    
    // اختبار 6: منع الوصول بـ token غير صالح
    console.log('\n🔒 اختبار 6: منع الوصول بـ token غير صالح');
    try {
      const invalidTokenResponse = await axios.get(`${BASE_URL}/api/roles`, {
        headers: { 'Authorization': 'Bearer invalid-token-123' }
      });
      console.log('⚠️ تم السماح بالوصول بـ token غير صالح (هذا خطأ!)');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ تم منع الوصول بـ token غير صالح بنجاح');
      } else {
        console.error('❌ خطأ غير متوقع:', error.response?.data);
      }
    }
    
    // اختبار 7: منع المستخدم العادي من الوصول لإدارة المستخدمين
    console.log('\n👥 اختبار 7: منع المستخدم العادي من الوصول لإدارة المستخدمين');
    try {
      const usersResponse = await axios.get(`${BASE_URL}/api/users`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log('⚠️ المستخدم العادي يمكنه الوصول لإدارة المستخدمين (هذا خطأ!)');
    } catch (error) {
      if (error.response?.status === 403) {
        console.log('✅ تم منع المستخدم العادي من الوصول لإدارة المستخدمين:', error.response.data.message);
      } else {
        console.error('❌ خطأ غير متوقع:', error.response?.data);
      }
    }
    
    // اختبار 8: المدير يمكنه الوصول لإدارة المستخدمين
    console.log('\n👑 اختبار 8: المدير يمكنه الوصول لإدارة المستخدمين');
    try {
      const usersResponse = await axios.get(`${BASE_URL}/api/users`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log(`✅ المدير يمكنه الوصول لإدارة المستخدمين - تم جلب ${usersResponse.data.length} مستخدم`);
    } catch (error) {
      console.error('❌ المدير لا يمكنه الوصول لإدارة المستخدمين:', error.response?.data);
    }
    
    console.log('\n🎉 انتهى اختبار الأمان الجديد');
    console.log('\n📊 ملخص النتائج:');
    console.log('✅ تم تطبيق الحماية بنجاح على جميع APIs');
    console.log('✅ المدير يمكنه الوصول لجميع الوظائف');
    console.log('✅ المستخدم العادي محدود الصلاحيات');
    console.log('✅ منع الوصول غير المصرح به');
    
  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testSecurityImplementation();
