<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الإشعارات المحسنة</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { 
            font-family: 'IBM Plex Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            min-height: 100vh;
            color: #1e293b;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border-radius: 15px;
            color: white;
        }
        .test-section {
            background: rgba(248, 250, 252, 0.8);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid #e2e8f0;
        }
        .notification-demo {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .notification-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .notification-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .notification-preview {
            border-radius: 12px;
            padding: 16px;
            margin: 15px 0;
            display: flex;
            align-items: start;
            gap: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid;
        }
        .success-preview {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.2);
            color: #166534;
        }
        .error-preview {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.2);
            color: #991b1b;
        }
        .warning-preview {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.2);
            color: #92400e;
        }
        .info-preview {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.2);
            color: #1e40af;
        }
        .icon {
            width: 20px;
            height: 20px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        .notification-content {
            flex: 1;
        }
        .notification-title {
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
        }
        .notification-desc {
            font-size: 13px;
            opacity: 0.9;
            line-height: 1.4;
        }
        .test-button {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .demo-section {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }
        .feature-list {
            text-align: right;
            line-height: 2;
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
        }
        .feature-list li {
            margin: 10px 0;
            padding: 8px;
            background: rgba(59, 130, 246, 0.05);
            border-radius: 8px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            font-weight: 600;
            color: #1e293b;
        }
        .old-feature {
            color: #ef4444;
        }
        .new-feature {
            color: #10b981;
            font-weight: 600;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            background: #dcfce7;
            color: #166534;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔔 نظام الإشعارات المحسن</h1>
            <p>إشعارات ذكية مع دعم كامل للغة العربية والإنجليزية</p>
        </div>

        <div class="demo-section">
            <h2>🚀 جرب الإشعارات المحسنة</h2>
            <p>اضغط على الأزرار أدناه لاختبار أنواع الإشعارات المختلفة</p>
            <button class="test-button" onclick="testLogin()">🔐 اختبار تسجيل الدخول</button>
            <button class="test-button" onclick="testLoginError()">❌ اختبار خطأ تسجيل الدخول</button>
            <a href="http://localhost:5173/login" class="test-button" target="_blank">
                🔗 فتح صفحة تسجيل الدخول
            </a>
        </div>

        <div class="test-section">
            <h3>✨ الميزات الجديدة في نظام الإشعارات</h3>
            <div class="notification-demo">
                <div class="notification-card">
                    <h4>🎯 إشعار النجاح</h4>
                    <div class="notification-preview success-preview">
                        <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                        <div class="notification-content">
                            <div class="notification-title">تم تسجيل الدخول بنجاح</div>
                            <div class="notification-desc">مرحباً بك في النظام</div>
                        </div>
                    </div>
                    <p>إشعارات النجاح مع أيقونات واضحة وألوان مريحة</p>
                </div>

                <div class="notification-card">
                    <h4>⚠️ إشعار الخطأ</h4>
                    <div class="notification-preview error-preview">
                        <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                        </svg>
                        <div class="notification-content">
                            <div class="notification-title">بيانات الدخول غير صحيحة</div>
                            <div class="notification-desc">تأكد من البريد الإلكتروني وكلمة المرور</div>
                        </div>
                    </div>
                    <p>رسائل خطأ واضحة ومفيدة باللغة العربية</p>
                </div>

                <div class="notification-card">
                    <h4>💡 إشعار التحذير</h4>
                    <div class="notification-preview warning-preview">
                        <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <div class="notification-content">
                            <div class="notification-title">تحذير</div>
                            <div class="notification-desc">يرجى المراجعة قبل المتابعة</div>
                        </div>
                    </div>
                    <p>تحذيرات مفيدة مع إرشادات واضحة</p>
                </div>

                <div class="notification-card">
                    <h4>ℹ️ إشعار المعلومات</h4>
                    <div class="notification-preview info-preview">
                        <svg class="icon" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                        </svg>
                        <div class="notification-content">
                            <div class="notification-title">معلومات</div>
                            <div class="notification-desc">معلومات إضافية مفيدة</div>
                        </div>
                    </div>
                    <p>معلومات إضافية مع تصميم هادئ</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 مقارنة بين النظام القديم والجديد</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>النظام القديم</th>
                        <th>النظام الجديد</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>الأيقونات</td>
                        <td class="old-feature">لا توجد أيقونات</td>
                        <td class="new-feature">أيقونات واضحة لكل نوع</td>
                        <td><span class="status-badge">جديد</span></td>
                    </tr>
                    <tr>
                        <td>دعم RTL</td>
                        <td class="old-feature">محدود</td>
                        <td class="new-feature">دعم كامل للعربية</td>
                        <td><span class="status-badge">محسن</span></td>
                    </tr>
                    <tr>
                        <td>الترجمات</td>
                        <td class="old-feature">إنجليزية فقط</td>
                        <td class="new-feature">عربي + إنجليزي</td>
                        <td><span class="status-badge">محسن</span></td>
                    </tr>
                    <tr>
                        <td>معالجة الأخطاء</td>
                        <td class="old-feature">رسائل عامة</td>
                        <td class="new-feature">رسائل ذكية ومخصصة</td>
                        <td><span class="status-badge">محسن</span></td>
                    </tr>
                    <tr>
                        <td>التصميم</td>
                        <td class="old-feature">بسيط</td>
                        <td class="new-feature">متطور مع تأثيرات</td>
                        <td><span class="status-badge">محسن</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🎯 التحسينات المطبقة</h3>
            <ul class="feature-list">
                <li>✅ <strong>أيقونات ذكية:</strong> أيقونة مناسبة لكل نوع إشعار</li>
                <li>✅ <strong>ترجمات محسنة:</strong> رسائل واضحة باللغة العربية</li>
                <li>✅ <strong>معالجة أخطاء ذكية:</strong> تحليل نوع الخطأ وعرض رسالة مناسبة</li>
                <li>✅ <strong>دعم RTL كامل:</strong> تخطيط صحيح للغة العربية</li>
                <li>✅ <strong>تصميم متطور:</strong> ألوان وتأثيرات بصرية محسنة</li>
                <li>✅ <strong>سهولة الاستخدام:</strong> Hook محسن للاستخدام السريع</li>
                <li>✅ <strong>تجربة مستخدم أفضل:</strong> إشعارات واضحة ومفيدة</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🎉 النتيجة النهائية</h2>
            <p>نظام إشعارات متطور يوفر تجربة مستخدم ممتازة</p>
            <div style="margin-top: 20px;">
                <span class="status-badge">✅ إشعارات محسنة</span>
                <span class="status-badge">✅ دعم متعدد اللغات</span>
                <span class="status-badge">✅ تجربة مستخدم متقدمة</span>
            </div>
        </div>
    </div>

    <script>
        function testLogin() {
            alert('🔐 سيتم اختبار تسجيل الدخول في الصفحة الفعلية')
        }
        
        function testLoginError() {
            alert('❌ سيتم اختبار خطأ تسجيل الدخول في الصفحة الفعلية')
        }
    </script>
</body>
</html>
