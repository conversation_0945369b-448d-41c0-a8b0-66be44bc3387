const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

// Create a separate test database instance
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.TEST_DATABASE_URL || process.env.DATABASE_URL
    }
  }
});

/**
 * Setup test database with clean data
 */
async function setupTestDatabase() {
  try {
    // Clean existing data in correct order
    await prisma.userRole.deleteMany();
    await prisma.user.deleteMany();
    await prisma.role.deleteMany();

    // Create test roles first
    const adminRole = await prisma.role.create({
      data: {
        name: 'admin',
        description: 'System Administrator',
        permissions: {
          users: ['create', 'read', 'update', 'delete'],
          roles: ['create', 'read', 'update', 'delete'],
          modules: ['create', 'read', 'update', 'delete', 'install', 'uninstall'],
          dashboard: ['read'],
          audit: ['read'],
          settings: ['read', 'update'],
          integrations: ['read', 'update']
        }
      }
    });

    const userRole = await prisma.role.create({
      data: {
        name: 'user',
        description: 'Regular User',
        permissions: {
          dashboard: ['read']
        }
      }
    });

    // Create test users
    const hashedPassword = await bcrypt.hash('testpassword', 12);

    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword
      }
    });

    const regularUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword
      }
    });

    // Assign roles to users
    await prisma.userRole.create({
      data: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    });

    await prisma.userRole.create({
      data: {
        userId: regularUser.id,
        roleId: userRole.id
      }
    });

    return {
      adminUser,
      regularUser,
      adminRole,
      userRole
    };
  } catch (error) {
    console.error('Error setting up test database:', error);
    throw error;
  }
}

/**
 * Clean up test database
 */
async function cleanupTestDatabase() {
  try {
    // Delete in correct order to avoid foreign key constraints
    await prisma.userRole.deleteMany();
    await prisma.user.deleteMany();
    await prisma.role.deleteMany();

    // Reset auto-increment sequences if using SQLite
    try {
      await prisma.$executeRaw`DELETE FROM sqlite_sequence WHERE name IN ('User', 'Role', 'UserRole')`;
    } catch (e) {
      // Ignore if not SQLite or table doesn't exist
    }
  } catch (error) {
    console.error('Error cleaning up test database:', error);
    // Don't throw error to allow tests to continue
  }
}

/**
 * Reset database completely
 */
async function resetDatabase() {
  try {
    await cleanupTestDatabase();
    // Wait a bit to ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 100));
  } catch (error) {
    console.error('Error resetting database:', error);
  }
}

/**
 * Close database connection
 */
async function closeDatabase() {
  await prisma.$disconnect();
}

module.exports = {
  prisma,
  setupTestDatabase,
  cleanupTestDatabase,
  resetDatabase,
  closeDatabase
};
