<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Login</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Test Login</h1>
    <form id="loginForm">
        <div>
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label for="password">Password:</label>
            <input type="password" id="password" value="admin123" required>
        </div>
        <button type="submit">Login</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Attempting login with:', { email, password });
                
                const response = await axios.post('http://localhost:3000/api/auth/login', {
                    email,
                    password
                });
                
                console.log('Login response:', response);
                console.log('Response data:', response.data);
                
                resultDiv.innerHTML = `
                    <h3>Login Successful!</h3>
                    <p>Access Token: ${response.data.accessToken ? 'Present' : 'Missing'}</p>
                    <p>Refresh Token: ${response.data.refreshToken ? 'Present' : 'Missing'}</p>
                    <p>Email: ${response.data.email}</p>
                    <pre>${JSON.stringify(response.data, null, 2)}</pre>
                `;
                
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.innerHTML = `
                    <h3>Login Failed!</h3>
                    <p>Error: ${error.response?.data?.message || error.message}</p>
                    <pre>${JSON.stringify(error.response?.data || error, null, 2)}</pre>
                `;
            }
        });
    </script>
</body>
</html>
