require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

async function createSampleData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🎯 Creating sample data for testing...');
    
    // Create sample admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: "password"
        isActive: true,
      }
    });

    console.log(`✅ Created admin user: ${adminUser.email}`);

    // Create sample regular user
    const regularUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: "password"
        isActive: true,
      }
    });

    console.log(`✅ Created regular user: ${regularUser.email}`);

    // Create sample roles
    const adminRole = await prisma.role.create({
      data: {
        name: 'admin',
        description: 'System Administrator',
        permissions: {
          users: ['create', 'read', 'update', 'delete'],
          roles: ['create', 'read', 'update', 'delete'],
          modules: ['create', 'read', 'update', 'delete'],
          settings: ['create', 'read', 'update', 'delete']
        }
      }
    });

    const userRole = await prisma.role.create({
      data: {
        name: 'user',
        description: 'Regular User',
        permissions: {
          profile: ['read', 'update'],
          dashboard: ['read']
        }
      }
    });

    console.log(`✅ Created roles: admin, user`);

    // Assign roles to users
    await prisma.userRole.create({
      data: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    });

    await prisma.userRole.create({
      data: {
        userId: regularUser.id,
        roleId: userRole.id
      }
    });

    console.log(`✅ Assigned roles to users`);

    // Create sample modules
    const modules = [
      { name: 'dashboard', displayName: 'Dashboard', route: '/', icon: 'home' },
      { name: 'users', displayName: 'User Management', route: '/users', icon: 'users' },
      { name: 'roles', displayName: 'Role Management', route: '/roles', icon: 'shield' },
      { name: 'modules', displayName: 'Module Management', route: '/modules', icon: 'grid' },
      { name: 'audit', displayName: 'Audit Logs', route: '/audit', icon: 'file-text' },
      { name: 'settings', displayName: 'System Settings', route: '/settings', icon: 'settings' },
    ];

    for (const module of modules) {
      await prisma.module.create({ data: module });
    }

    console.log(`✅ Created ${modules.length} sample modules`);

    console.log('\n🎉 Sample data created successfully!');
    console.log('\n🔑 Test Credentials:');
    console.log('   Admin: <EMAIL> / password');
    console.log('   User:  <EMAIL> / password');

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createSampleData();
