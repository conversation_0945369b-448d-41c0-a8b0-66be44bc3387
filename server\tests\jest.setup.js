const { closeDatabase } = require('./setup');

// Global setup before all tests
beforeAll(async () => {
  // Set test environment
  process.env.NODE_ENV = 'test';
});

// Global cleanup after all tests
afterAll(async () => {
  // Close database connections
  await closeDatabase();
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.log('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.log('Uncaught Exception:', error);
});
