const express = require('express');
const { requireUser } = require('./middleware/auth.js');

const router = express.Router();

// Simple test route to verify module routes are working
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Module routes are working!'
  });
});

// Get modules for navigation
router.get('/navigation', requireUser, async (req, res) => {
  try {
    // Return empty array for now to test the route
    res.json({
      success: true,
      modules: []
    });
  } catch (error) {
    console.error('Error fetching navigation modules:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

// Get all modules
router.get('/', requireUser, async (req, res) => {
  try {
    // Return empty array for now to test the route
    res.json({
      success: true,
      modules: []
    });
  } catch (error) {
    console.error('Error fetching modules:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
