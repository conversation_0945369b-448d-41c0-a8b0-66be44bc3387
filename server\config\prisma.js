const { PrismaClient } = require('@prisma/client');

// Create a single instance of Prisma Client with optimized configuration
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['warn', 'error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
});

// Query optimization middleware
prisma.$use(async (params, next) => {
  const before = Date.now();
  const result = await next(params);
  const after = Date.now();

  // Log slow queries (>100ms)
  if (after - before > 100) {
    console.warn(`Slow query detected: ${params.model}.${params.action} took ${after - before}ms`);
  }

  return result;
});

// Test connection function
const connectDB = async () => {
  try {
    await prisma.$connect();
    console.log('PostgreSQL Connected via Prisma');
    
    // Test the connection with a simple query
    await prisma.$queryRaw`SELECT 1`;
    console.log('Database connection test successful');
    
  } catch (error) {
    console.error(`Error connecting to PostgreSQL: ${error.message}`);
    process.exit(1);
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

module.exports = {
  prisma,
  connectDB,
};
