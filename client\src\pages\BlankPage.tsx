import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Construction, ArrowLeft } from "lucide-react"
import { useNavigate, useLocation } from "react-router-dom"
import { useLanguage } from "@/contexts/LanguageContext"

export function BlankPage() {
  const navigate = useNavigate()
  const location = useLocation()
  const { t } = useLanguage()

  const getPageTitle = () => {
    const path = location.pathname
    switch (path) {
      case '/analytics':
        return 'Analytics Dashboard'
      case '/reports':
        return 'Reports'
      case '/calendar':
        return 'Calendar'
      default:
        return 'Page'
    }
  }

  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md mx-auto bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
            <Construction className="w-8 h-8 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent dark:from-slate-100 dark:to-slate-400">
            {getPageTitle()}
          </CardTitle>
          <CardDescription className="text-slate-600 dark:text-slate-400">
            This module page is under construction. The module has been installed but the interface is not yet available.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center">
          <Button 
            onClick={() => navigate("/")}
            className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}