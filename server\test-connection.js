require('dotenv').config();
const { PrismaClient } = require('@prisma/client');

async function testConnection() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Testing PostgreSQL connection...');
    console.log('Database URL:', process.env.DATABASE_URL);
    
    await prisma.$connect();
    console.log('✅ Connected to PostgreSQL successfully!');
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ Query test successful:', result);
    
    // Check if tables exist
    const users = await prisma.user.findMany();
    console.log(`📊 Found ${users.length} users in database`);
    
  } catch (error) {
    console.error('❌ Connection failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Disconnected from database');
  }
}

testConnection();
