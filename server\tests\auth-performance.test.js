/**
 * Authentication Performance Tests
 * اختبارات أداء نظام المصادقة
 */

const request = require('supertest');
const app = require('../app');
const { setupTestDatabase } = require('./setup');

describe('Authentication Performance Tests', () => {
  let adminToken;
  let testUser;

  beforeAll(async () => {
    await setupTestDatabase();
    
    // Login to get admin token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'admin123'
      });

    adminToken = loginResponse.body.data.accessToken;
    testUser = loginResponse.body.data.user;
  });

  describe('Token Verification Performance', () => {
    test('should verify token in under 50ms', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${adminToken}`);

      const duration = Date.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(50);
    });

    test('should handle concurrent token verifications efficiently', async () => {
      const concurrentRequests = 10;
      const start = Date.now();
      
      const promises = Array(concurrentRequests).fill().map(() =>
        request(app)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${adminToken}`)
      );

      const responses = await Promise.all(promises);
      const duration = Date.now() - start;
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // Average time per request should be reasonable
      const avgTime = duration / concurrentRequests;
      expect(avgTime).toBeLessThan(100);
    });
  });

  describe('Login Performance', () => {
    test('should complete login in under 200ms', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'admin123'
        });

      const duration = Date.now() - start;
      
      expect(response.status).toBe(200);
      expect(duration).toBeLessThan(200);
    });

    test('should handle multiple login attempts efficiently', async () => {
      const concurrentLogins = 5;
      const start = Date.now();
      
      const promises = Array(concurrentLogins).fill().map(() =>
        request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'admin123'
          })
      );

      const responses = await Promise.all(promises);
      const duration = Date.now() - start;
      
      // All logins should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.data.accessToken).toBeDefined();
      });
      
      // Total time should be reasonable
      expect(duration).toBeLessThan(1000);
    });
  });

  describe('Database Query Performance', () => {
    test('should fetch user data efficiently', async () => {
      const start = Date.now();
      
      // Multiple requests to test caching
      for (let i = 0; i < 5; i++) {
        const response = await request(app)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${adminToken}`);
        
        expect(response.status).toBe(200);
      }
      
      const duration = Date.now() - start;
      
      // Should benefit from caching after first request
      expect(duration).toBeLessThan(250);
    });
  });

  describe('Rate Limiting Performance', () => {
    test('should apply rate limiting without significant overhead', async () => {
      const start = Date.now();
      
      // Make requests within rate limit
      const promises = Array(5).fill().map(() =>
        request(app)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${adminToken}`)
      );

      const responses = await Promise.all(promises);
      const duration = Date.now() - start;
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // Rate limiting shouldn't add significant overhead
      expect(duration).toBeLessThan(500);
    });
  });

  describe('Memory Usage', () => {
    test('should not leak memory during authentication', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform many authentication operations
      for (let i = 0; i < 50; i++) {
        await request(app)
          .get('/api/auth/me')
          .set('Authorization', `Bearer ${adminToken}`);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      
      // Memory increase should be minimal (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024);
    });
  });

  describe('Response Compression', () => {
    test('should compress large responses', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('Accept-Encoding', 'gzip');

      // Check if response is compressed
      expect(response.headers['content-encoding']).toBeDefined();
    });
  });

  describe('Error Handling Performance', () => {
    test('should handle invalid tokens quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');

      const duration = Date.now() - start;
      
      expect(response.status).toBe(401);
      expect(duration).toBeLessThan(50);
    });

    test('should handle missing tokens quickly', async () => {
      const start = Date.now();
      
      const response = await request(app)
        .get('/api/auth/me');

      const duration = Date.now() - start;
      
      expect(response.status).toBe(401);
      expect(duration).toBeLessThan(10);
    });
  });
});
