/**
 * Performance Monitoring Middleware
 * مراقبة أداء النظام
 */

const { logSecurityEvent } = require('./auditLogger');

// Performance metrics storage
const performanceMetrics = {
  requests: {
    total: 0,
    successful: 0,
    failed: 0,
    averageResponseTime: 0,
    slowQueries: 0
  },
  authentication: {
    logins: 0,
    loginFailures: 0,
    tokenVerifications: 0,
    averageLoginTime: 0,
    averageVerificationTime: 0
  },
  database: {
    queries: 0,
    slowQueries: 0,
    averageQueryTime: 0,
    connectionPoolUsage: 0
  },
  memory: {
    heapUsed: 0,
    heapTotal: 0,
    external: 0,
    rss: 0
  },
  errors: {
    total: 0,
    authentication: 0,
    database: 0,
    validation: 0,
    server: 0
  }
};

// Response time tracking
const responseTimeTracker = [];
const MAX_RESPONSE_TIME_SAMPLES = 1000;

/**
 * Request performance monitoring middleware
 * مراقبة أداء الطلبات
 */
const monitorRequestPerformance = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Update metrics
    performanceMetrics.requests.total++;
    
    if (res.statusCode < 400) {
      performanceMetrics.requests.successful++;
    } else {
      performanceMetrics.requests.failed++;
      
      // Categorize errors
      if (res.statusCode === 401 || res.statusCode === 403) {
        performanceMetrics.errors.authentication++;
      } else if (res.statusCode >= 500) {
        performanceMetrics.errors.server++;
      }
      
      performanceMetrics.errors.total++;
    }
    
    // Track response times
    responseTimeTracker.push(responseTime);
    if (responseTimeTracker.length > MAX_RESPONSE_TIME_SAMPLES) {
      responseTimeTracker.shift();
    }
    
    // Calculate average response time
    const totalTime = responseTimeTracker.reduce((sum, time) => sum + time, 0);
    performanceMetrics.requests.averageResponseTime = totalTime / responseTimeTracker.length;
    
    // Log slow requests
    if (responseTime > 1000) {
      console.warn(`Slow request detected: ${req.method} ${req.originalUrl} took ${responseTime}ms`);
      performanceMetrics.requests.slowQueries++;
    }
    
    // Set performance headers
    res.setHeader('X-Response-Time', `${responseTime}ms`);
    res.setHeader('X-Request-ID', req.id || 'unknown');
    
    return originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Authentication performance monitoring
 * مراقبة أداء المصادقة
 */
const monitorAuthPerformance = (operation) => {
  return (req, res, next) => {
    const startTime = Date.now();
    
    const originalSend = res.send;
    res.send = function(data) {
      const duration = Date.now() - startTime;
      
      if (operation === 'login') {
        performanceMetrics.authentication.logins++;
        
        // Track login times
        const currentAvg = performanceMetrics.authentication.averageLoginTime;
        const count = performanceMetrics.authentication.logins;
        performanceMetrics.authentication.averageLoginTime = 
          ((currentAvg * (count - 1)) + duration) / count;
        
        if (res.statusCode >= 400) {
          performanceMetrics.authentication.loginFailures++;
        }
        
        // Log slow logins
        if (duration > 500) {
          console.warn(`Slow login detected: ${duration}ms for ${req.body?.email}`);
        }
      } else if (operation === 'verify') {
        performanceMetrics.authentication.tokenVerifications++;
        
        // Track verification times
        const currentAvg = performanceMetrics.authentication.averageVerificationTime;
        const count = performanceMetrics.authentication.tokenVerifications;
        performanceMetrics.authentication.averageVerificationTime = 
          ((currentAvg * (count - 1)) + duration) / count;
        
        // Log slow verifications
        if (duration > 100) {
          console.warn(`Slow token verification: ${duration}ms`);
        }
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  };
};

/**
 * Memory usage monitoring
 * مراقبة استخدام الذاكرة
 */
const updateMemoryMetrics = () => {
  const memUsage = process.memoryUsage();
  performanceMetrics.memory = {
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
    heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
    external: Math.round(memUsage.external / 1024 / 1024), // MB
    rss: Math.round(memUsage.rss / 1024 / 1024) // MB
  };
};

/**
 * Get performance metrics
 * الحصول على مقاييس الأداء
 */
const getPerformanceMetrics = () => {
  updateMemoryMetrics();
  
  return {
    ...performanceMetrics,
    uptime: Math.round(process.uptime()),
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch
  };
};

/**
 * Reset performance metrics
 * إعادة تعيين مقاييس الأداء
 */
const resetPerformanceMetrics = () => {
  performanceMetrics.requests = {
    total: 0,
    successful: 0,
    failed: 0,
    averageResponseTime: 0,
    slowQueries: 0
  };
  
  performanceMetrics.authentication = {
    logins: 0,
    loginFailures: 0,
    tokenVerifications: 0,
    averageLoginTime: 0,
    averageVerificationTime: 0
  };
  
  performanceMetrics.errors = {
    total: 0,
    authentication: 0,
    database: 0,
    validation: 0,
    server: 0
  };
  
  responseTimeTracker.length = 0;
};

/**
 * Performance alert system
 * نظام تنبيهات الأداء
 */
const checkPerformanceAlerts = () => {
  const metrics = getPerformanceMetrics();
  
  // High error rate alert
  if (metrics.requests.total > 100) {
    const errorRate = (metrics.requests.failed / metrics.requests.total) * 100;
    if (errorRate > 10) {
      console.error(`HIGH ERROR RATE ALERT: ${errorRate.toFixed(2)}% error rate`);
    }
  }
  
  // High memory usage alert
  if (metrics.memory.heapUsed > 500) {
    console.warn(`HIGH MEMORY USAGE: ${metrics.memory.heapUsed}MB heap used`);
  }
  
  // Slow response time alert
  if (metrics.requests.averageResponseTime > 500) {
    console.warn(`SLOW RESPONSE TIME: ${metrics.requests.averageResponseTime.toFixed(2)}ms average`);
  }
  
  // High authentication failure rate
  if (metrics.authentication.logins > 50) {
    const failureRate = (metrics.authentication.loginFailures / metrics.authentication.logins) * 100;
    if (failureRate > 20) {
      console.error(`HIGH AUTH FAILURE RATE: ${failureRate.toFixed(2)}% login failure rate`);
    }
  }
};

// Run performance checks every 5 minutes (only in production)
if (process.env.NODE_ENV !== 'test') {
  setInterval(checkPerformanceAlerts, 5 * 60 * 1000);

  // Update memory metrics every minute
  setInterval(updateMemoryMetrics, 60 * 1000);
}

module.exports = {
  monitorRequestPerformance,
  monitorAuthPerformance,
  getPerformanceMetrics,
  resetPerformanceMetrics,
  checkPerformanceAlerts
};
