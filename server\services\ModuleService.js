const { PrismaClient } = require('@prisma/client');
const fs = require('fs').promises;
const path = require('path');
const AdmZip = require('adm-zip');

const prisma = new PrismaClient();

class ModuleService {
  /**
   * Get all modules with optional filtering
   * @param {Object} filters - Filter options
   * @returns {Promise<Array>} List of modules
   */
  async getAllModules(filters = {}) {
    try {
      const { category, status, search } = filters;
      
      const whereClause = {};
      
      if (category && category !== 'all') {
        whereClause.category = category;
      }
      
      if (status && status !== 'all') {
        whereClause.status = status;
      }
      
      if (search) {
        whereClause.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { displayName: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ];
      }

      const modules = await prisma.module.findMany({
        where: whereClause,
        include: {
          uploader: {
            select: {
              id: true,
              email: true
            }
          }
        },
        orderBy: [
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ]
      });

      return modules;
    } catch (error) {
      console.error('Error fetching modules:', error);
      throw new Error('Failed to fetch modules');
    }
  }

  /**
   * Get module by ID
   * @param {string} moduleId - Module ID
   * @returns {Promise<Object>} Module data
   */
  async getModuleById(moduleId) {
    try {
      const module = await prisma.module.findUnique({
        where: { id: moduleId },
        include: {
          uploader: {
            select: {
              id: true,
              email: true
            }
          }
        }
      });

      if (!module) {
        throw new Error('Module not found');
      }

      return module;
    } catch (error) {
      console.error('Error fetching module:', error);
      throw error;
    }
  }

  /**
   * Create a new module
   * @param {Object} moduleData - Module data
   * @param {string} userId - User ID who is creating the module
   * @returns {Promise<Object>} Created module
   */
  async createModule(moduleData, userId) {
    try {
      const module = await prisma.module.create({
        data: {
          ...moduleData,
          uploadedBy: userId,
          installedAt: new Date(),
          lastUpdate: new Date()
        },
        include: {
          uploader: {
            select: {
              id: true,
              email: true
            }
          }
        }
      });

      return module;
    } catch (error) {
      console.error('Error creating module:', error);
      throw new Error('Failed to create module');
    }
  }

  /**
   * Update module
   * @param {string} moduleId - Module ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Updated module
   */
  async updateModule(moduleId, updateData) {
    try {
      const module = await prisma.module.update({
        where: { id: moduleId },
        data: {
          ...updateData,
          lastUpdate: new Date()
        },
        include: {
          uploader: {
            select: {
              id: true,
              email: true
            }
          }
        }
      });

      return module;
    } catch (error) {
      console.error('Error updating module:', error);
      throw new Error('Failed to update module');
    }
  }

  /**
   * Toggle module active status
   * @param {string} moduleId - Module ID
   * @param {boolean} enabled - Enable/disable status
   * @returns {Promise<Object>} Updated module
   */
  async toggleModule(moduleId, enabled) {
    try {
      const updateData = {
        isActive: enabled,
        status: enabled ? 'active' : 'inactive',
        lastUpdate: new Date()
      };

      const module = await prisma.module.update({
        where: { id: moduleId },
        data: updateData
      });

      return module;
    } catch (error) {
      console.error('Error toggling module:', error);
      throw new Error('Failed to toggle module status');
    }
  }

  /**
   * Delete module
   * @param {string} moduleId - Module ID
   * @returns {Promise<boolean>} Success status
   */
  async deleteModule(moduleId) {
    try {
      // Get module info before deletion to clean up files
      const module = await prisma.module.findUnique({
        where: { id: moduleId }
      });

      if (!module) {
        throw new Error('Module not found');
      }

      // Delete module files if they exist
      if (module.filePath) {
        try {
          await fs.unlink(module.filePath);
        } catch (fileError) {
          console.warn('Could not delete module file:', fileError.message);
        }
      }

      // Delete from database
      await prisma.module.delete({
        where: { id: moduleId }
      });

      return true;
    } catch (error) {
      console.error('Error deleting module:', error);
      throw new Error('Failed to delete module');
    }
  }

  /**
   * Upload and install module from ZIP file
   * @param {Object} file - Uploaded file object
   * @param {string} userId - User ID who is uploading
   * @returns {Promise<Object>} Created module
   */
  async uploadModule(file, userId) {
    try {
      // Create uploads directory if it doesn't exist
      const uploadsDir = path.join(__dirname, '../uploads/modules');
      await fs.mkdir(uploadsDir, { recursive: true });

      // Save uploaded file
      const fileName = `${Date.now()}-${file.originalname}`;
      const filePath = path.join(uploadsDir, fileName);
      await fs.writeFile(filePath, file.buffer);

      // Extract and validate ZIP file
      const zip = new AdmZip(filePath);
      const zipEntries = zip.getEntries();
      
      // Look for module.json configuration file
      const configEntry = zipEntries.find(entry => entry.entryName === 'module.json');
      if (!configEntry) {
        throw new Error('Invalid module: module.json not found');
      }

      const moduleConfig = JSON.parse(configEntry.getData().toString());
      
      // Validate required fields
      if (!moduleConfig.name || !moduleConfig.version) {
        throw new Error('Invalid module: name and version are required');
      }

      // Calculate file size
      const stats = await fs.stat(filePath);
      const fileSize = this.formatFileSize(stats.size);

      // Create module record
      const moduleData = {
        name: moduleConfig.name,
        displayName: moduleConfig.displayName || moduleConfig.name,
        version: moduleConfig.version,
        description: moduleConfig.description || '',
        author: moduleConfig.author || 'Unknown',
        category: moduleConfig.category || 'general',
        icon: moduleConfig.icon || 'Package',
        route: moduleConfig.route || `/${moduleConfig.name.toLowerCase()}`,
        dependencies: moduleConfig.dependencies || [],
        config: moduleConfig.config || {},
        size: fileSize,
        filePath: filePath,
        status: 'inactive',
        health: 100
      };

      const module = await this.createModule(moduleData, userId);

      return module;
    } catch (error) {
      console.error('Error uploading module:', error);
      throw error;
    }
  }

  /**
   * Get module health metrics
   * @param {string} moduleId - Module ID
   * @returns {Promise<Object>} Health metrics
   */
  async getModuleHealth(moduleId) {
    try {
      const module = await this.getModuleById(moduleId);
      
      // Simulate health metrics (in real implementation, this would check actual module status)
      const metrics = {
        health: module.health,
        metrics: {
          cpu: Math.random() * 20, // 0-20%
          memory: Math.random() * 100, // 0-100MB
          responseTime: Math.floor(Math.random() * 200) + 50 // 50-250ms
        },
        errors: module.status === 'error' ? ['Module initialization failed'] : [],
        lastCheck: new Date().toISOString()
      };

      return metrics;
    } catch (error) {
      console.error('Error getting module health:', error);
      throw error;
    }
  }

  /**
   * Get modules for navigation
   * @returns {Promise<Array>} Navigation modules
   */
  async getNavigationModules() {
    try {
      const modules = await prisma.module.findMany({
        where: {
          isActive: true,
          status: 'active'
        },
        select: {
          id: true,
          name: true,
          displayName: true,
          icon: true,
          route: true,
          sortOrder: true
        },
        orderBy: {
          sortOrder: 'asc'
        }
      });

      return modules.map(module => ({
        _id: module.id,
        name: module.displayName,
        icon: module.icon,
        path: module.route,
        order: module.sortOrder
      }));
    } catch (error) {
      console.error('Error fetching navigation modules:', error);
      throw new Error('Failed to fetch navigation modules');
    }
  }

  /**
   * Format file size in human readable format
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get module categories
   * @returns {Promise<Array>} List of categories
   */
  async getModuleCategories() {
    try {
      const categories = await prisma.module.groupBy({
        by: ['category'],
        _count: {
          category: true
        }
      });

      return categories.map(cat => ({
        name: cat.category,
        count: cat._count.category
      }));
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw new Error('Failed to fetch categories');
    }
  }
}

module.exports = new ModuleService();
