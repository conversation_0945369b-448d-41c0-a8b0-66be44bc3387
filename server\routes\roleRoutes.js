const express = require('express');
const { prisma } = require('../config/prisma.js');
const { requireUser } = require('./middleware/auth.js');
const { checkRole, checkPermission, requireAdmin } = require('./middleware/roleCheck.js');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Roles
 *   description: Role management endpoints
 */

/**
 * @swagger
 * /api/roles:
 *   get:
 *     summary: Get all roles
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of roles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Role'
 */
router.get('/', requireUser, checkPermission('roles', 'read'), async (req, res) => {
  try {
    const roles = await prisma.role.findMany({
      orderBy: { createdAt: 'desc' }
    });
    res.json(roles);
  } catch (error) {
    console.error('Error fetching roles:', error);
    res.status(500).json({ error: 'Failed to fetch roles' });
  }
});

/**
 * @swagger
 * /api/roles:
 *   post:
 *     summary: Create new role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - permissions
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               permissions:
 *                 type: object
 *           example:
 *             name: "editor"
 *             description: "Content Editor"
 *             permissions:
 *               users: ["read"]
 *               content: ["create", "read", "update"]
 *     responses:
 *       201:
 *         description: Role created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Role'
 */
router.post('/', requireUser, checkPermission('roles', 'create'), async (req, res) => {
  try {
    const { name, description, permissions } = req.body;

    if (!name || !permissions) {
      return res.status(400).json({ error: 'Name and permissions are required' });
    }

    const role = await prisma.role.create({
      data: {
        name,
        description,
        permissions
      }
    });

    res.status(201).json(role);
  } catch (error) {
    console.error('Error creating role:', error);
    if (error.code === 'P2002') {
      return res.status(400).json({ error: 'Role with this name already exists' });
    }
    res.status(500).json({ error: 'Failed to create role' });
  }
});

/**
 * @swagger
 * /api/roles/{id}:
 *   put:
 *     summary: Update role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               permissions:
 *                 type: object
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Role updated successfully
 *       404:
 *         description: Role not found
 */
router.put('/:id', requireUser, checkPermission('roles', 'update'), async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const role = await prisma.role.update({
      where: { id },
      data: updateData
    });

    res.json(role);
  } catch (error) {
    console.error('Error updating role:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Role not found' });
    }
    res.status(500).json({ error: 'Failed to update role' });
  }
});

/**
 * @swagger
 * /api/roles/{id}:
 *   delete:
 *     summary: Delete role
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Role deleted successfully
 *       404:
 *         description: Role not found
 */
router.delete('/:id', requireUser, checkPermission('roles', 'delete'), async (req, res) => {
  try {
    const { id } = req.params;

    // Check if role is being used by any users
    const roleUsage = await prisma.userRole.findFirst({
      where: { roleId: id }
    });

    if (roleUsage) {
      return res.status(400).json({
        error: 'Cannot delete role that is assigned to users. Please remove all users from this role first.'
      });
    }

    await prisma.role.delete({
      where: { id }
    });

    res.json({ success: true, message: 'Role deleted successfully' });
  } catch (error) {
    console.error('Error deleting role:', error);
    if (error.code === 'P2025') {
      return res.status(404).json({ error: 'Role not found' });
    }
    res.status(500).json({ error: 'Failed to delete role' });
  }
});

/**
 * @swagger
 * /api/roles/permissions:
 *   get:
 *     summary: Get available permissions
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of available permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 permissions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 *                       category:
 *                         type: string
 *                       enabled:
 *                         type: boolean
 */
router.get('/permissions', requireUser, checkPermission('roles', 'read'), async (req, res) => {
  try {
    // Define available permissions for the system
    const permissions = [
      {
        id: 'user.create',
        name: 'Create Users',
        description: 'Ability to create new user accounts',
        category: 'users',
        enabled: true
      },
      {
        id: 'user.read',
        name: 'View Users',
        description: 'Ability to view user information',
        category: 'users',
        enabled: true
      },
      {
        id: 'user.update',
        name: 'Update Users',
        description: 'Ability to modify user information',
        category: 'users',
        enabled: true
      },
      {
        id: 'user.delete',
        name: 'Delete Users',
        description: 'Ability to delete user accounts',
        category: 'users',
        enabled: true
      },
      {
        id: 'role.create',
        name: 'Create Roles',
        description: 'Ability to create new roles',
        category: 'roles',
        enabled: true
      },
      {
        id: 'role.read',
        name: 'View Roles',
        description: 'Ability to view role information',
        category: 'roles',
        enabled: true
      },
      {
        id: 'role.update',
        name: 'Update Roles',
        description: 'Ability to modify role information',
        category: 'roles',
        enabled: true
      },
      {
        id: 'role.delete',
        name: 'Delete Roles',
        description: 'Ability to delete roles',
        category: 'roles',
        enabled: true
      },
      {
        id: 'module.create',
        name: 'Create Modules',
        description: 'Ability to create and upload new modules',
        category: 'modules',
        enabled: true
      },
      {
        id: 'module.view',
        name: 'View Module List',
        description: 'Ability to view and browse the module list',
        category: 'modules',
        enabled: true
      },
      {
        id: 'module.read',
        name: 'View Module Details',
        description: 'Ability to view detailed module information',
        category: 'modules',
        enabled: true
      },
      {
        id: 'module.update',
        name: 'Update Modules',
        description: 'Ability to modify module settings',
        category: 'modules',
        enabled: true
      },
      {
        id: 'module.delete',
        name: 'Delete Modules',
        description: 'Ability to delete modules',
        category: 'modules',
        enabled: true
      },
      {
        id: 'module.activate',
        name: 'Activate/Deactivate Modules',
        description: 'Ability to enable or disable modules',
        category: 'modules',
        enabled: true
      },
      {
        id: 'dashboard.read',
        name: 'View Dashboard',
        description: 'Ability to access dashboard and statistics',
        category: 'dashboard',
        enabled: true
      },
      {
        id: 'audit.read',
        name: 'View Audit Logs',
        description: 'Ability to view system audit logs',
        category: 'audit',
        enabled: true
      },
      {
        id: 'settings.read',
        name: 'View Settings',
        description: 'Ability to view system settings',
        category: 'settings',
        enabled: true
      },
      {
        id: 'settings.update',
        name: 'Update Settings',
        description: 'Ability to modify system settings',
        category: 'settings',
        enabled: true
      },
      {
        id: 'integration.read',
        name: 'View Integrations',
        description: 'Ability to view system integrations',
        category: 'integrations',
        enabled: true
      },
      {
        id: 'integration.update',
        name: 'Manage Integrations',
        description: 'Ability to configure and manage integrations',
        category: 'integrations',
        enabled: true
      }
    ];
    
    res.json(permissions);
  } catch (error) {
    console.error('Error fetching permissions:', error);
    res.status(500).json({ error: 'Failed to fetch permissions' });
  }
});

module.exports = router;
