<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رسائل الخطأ المحسنة</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .success { color: #4ade80; font-weight: bold; }
        .error { color: #f87171; font-weight: bold; }
        .info { color: #60a5fa; }
        .warning { color: #fbbf24; }
        .log { 
            margin: 10px 0; 
            padding: 15px; 
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-right: 4px solid #ccc; 
        }
        .log.success { border-right-color: #4ade80; }
        .log.error { border-right-color: #f87171; }
        .log.info { border-right-color: #60a5fa; }
        .log.warning { border-right-color: #fbbf24; }
        button { 
            margin: 10px; 
            padding: 12px 20px; 
            border: none;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار رسائل الخطأ المحسنة</h1>
        
        <div class="test-section">
            <h3>📝 اختبار رسائل تسجيل الدخول</h3>
            <button onclick="testEmptyFields()">1. اختبار الحقول الفارغة</button>
            <button onclick="testWrongPassword()">2. اختبار كلمة مرور خاطئة</button>
            <button onclick="testWrongEmail()">3. اختبار بريد إلكتروني خاطئ</button>
            <button onclick="testCorrectLogin()">4. اختبار دخول صحيح</button>
        </div>
        
        <div class="test-section">
            <button onclick="clearLogs()">🗑️ مسح السجلات</button>
        </div>
        
        <div id="logs"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.innerHTML = `<strong>${new Date().toLocaleTimeString('ar-SA')}:</strong> ${message}`;
            logsDiv.appendChild(logDiv);
            console.log(message);
            
            // Auto scroll to bottom
            logDiv.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
            log('🧹 تم مسح السجلات', 'info');
        }
        
        async function testEmptyFields() {
            log('🧪 اختبار الحقول الفارغة...', 'info');
            
            try {
                const response = await axios.post('http://localhost:3000/api/auth/login', {
                    email: '',
                    password: ''
                });
                log('❌ خطأ: كان يجب أن يفشل الطلب!', 'error');
            } catch (error) {
                const message = error.response?.data?.message || error.message;
                log(`✅ رسالة الخطأ المتوقعة: ${message}`, 'success');
            }
        }
        
        async function testWrongPassword() {
            log('🧪 اختبار كلمة مرور خاطئة...', 'info');
            
            try {
                const response = await axios.post('http://localhost:3000/api/auth/login', {
                    email: '<EMAIL>',
                    password: 'wrongpassword123'
                });
                log('❌ خطأ: كان يجب أن يفشل الطلب!', 'error');
            } catch (error) {
                const message = error.response?.data?.message || error.message;
                log(`✅ رسالة الخطأ المحسنة: ${message}`, 'success');
            }
        }
        
        async function testWrongEmail() {
            log('🧪 اختبار بريد إلكتروني خاطئ...', 'info');
            
            try {
                const response = await axios.post('http://localhost:3000/api/auth/login', {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                log('❌ خطأ: كان يجب أن يفشل الطلب!', 'error');
            } catch (error) {
                const message = error.response?.data?.message || error.message;
                log(`✅ رسالة الخطأ المحسنة: ${message}`, 'success');
            }
        }
        
        async function testCorrectLogin() {
            log('🧪 اختبار دخول صحيح...', 'info');
            
            try {
                const response = await axios.post('http://localhost:3000/api/auth/login', {
                    email: '<EMAIL>',
                    password: 'admin123'
                });
                
                log('✅ تم تسجيل الدخول بنجاح!', 'success');
                log(`📧 البريد الإلكتروني: ${response.data.email}`, 'info');
                log(`🆔 معرف المستخدم: ${response.data.id}`, 'info');
                log(`🔑 التوكن: ${response.data.accessToken.substring(0, 50)}...`, 'info');
                
            } catch (error) {
                const message = error.response?.data?.message || error.message;
                log(`❌ فشل تسجيل الدخول: ${message}`, 'error');
            }
        }
        
        // Auto-run on page load
        window.onload = function() {
            log('🚀 تم تحميل الصفحة. جاهز لاختبار الرسائل المحسنة!', 'info');
            log('💡 اضغط على الأزرار لاختبار رسائل الخطأ المختلفة', 'warning');
        };
    </script>
</body>
</html>
