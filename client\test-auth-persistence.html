<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auth Persistence</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <h1>Test Authentication Persistence</h1>
    
    <div id="status">Checking authentication...</div>
    
    <div id="loginSection" style="display: none;">
        <h2>Login</h2>
        <form id="loginForm">
            <div>
                <label for="email">Email:</label>
                <input type="email" id="email" value="<EMAIL>" required>
            </div>
            <div>
                <label for="password">Password:</label>
                <input type="password" id="password" value="admin123" required>
            </div>
            <button type="submit">Login</button>
        </form>
    </div>
    
    <div id="userSection" style="display: none;">
        <h2>User Info</h2>
        <div id="userInfo"></div>
        <button id="logoutBtn">Logout</button>
        <button id="refreshBtn">Refresh Page</button>
    </div>
    
    <div id="logs"></div>

    <script>
        const statusDiv = document.getElementById('status');
        const loginSection = document.getElementById('loginSection');
        const userSection = document.getElementById('userSection');
        const userInfoDiv = document.getElementById('userInfo');
        const logsDiv = document.getElementById('logs');
        
        function log(message) {
            console.log(message);
            logsDiv.innerHTML += `<p>${new Date().toLocaleTimeString()}: ${message}</p>`;
        }
        
        function isTokenValid(token) {
            try {
                const payload = JSON.parse(atob(token.split('.')[1]));
                const currentTime = Date.now() / 1000;
                return payload.exp > currentTime;
            } catch {
                return false;
            }
        }
        
        async function checkAuth() {
            log('Checking authentication...');
            const token = localStorage.getItem('accessToken');
            
            if (!token) {
                log('No token found');
                showLogin();
                return;
            }
            
            if (!isTokenValid(token)) {
                log('Token is expired');
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                showLogin();
                return;
            }
            
            log('Token is valid, fetching user data...');
            
            try {
                const response = await axios.get('http://localhost:3000/api/auth/me', {
                    headers: { Authorization: `Bearer ${token}` }
                });
                
                log('User data fetched successfully');
                showUser(response.data);
            } catch (error) {
                log(`Failed to fetch user data: ${error.response?.data?.message || error.message}`);
                localStorage.removeItem('accessToken');
                localStorage.removeItem('refreshToken');
                showLogin();
            }
        }
        
        function showLogin() {
            statusDiv.textContent = 'Not authenticated';
            loginSection.style.display = 'block';
            userSection.style.display = 'none';
        }
        
        function showUser(userData) {
            statusDiv.textContent = 'Authenticated';
            loginSection.style.display = 'none';
            userSection.style.display = 'block';
            userInfoDiv.innerHTML = `
                <p>Email: ${userData.email}</p>
                <p>ID: ${userData.id}</p>
                <p>Last Login: ${userData.lastLoginAt}</p>
            `;
        }
        
        // Login form handler
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                log('Attempting login...');
                const response = await axios.post('http://localhost:3000/api/auth/login', {
                    email,
                    password
                });
                
                log('Login successful');
                localStorage.setItem('accessToken', response.data.accessToken);
                localStorage.setItem('refreshToken', response.data.refreshToken);
                
                showUser(response.data);
            } catch (error) {
                log(`Login failed: ${error.response?.data?.message || error.message}`);
            }
        });
        
        // Logout handler
        document.getElementById('logoutBtn').addEventListener('click', () => {
            log('Logging out...');
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            showLogin();
        });
        
        // Refresh page handler
        document.getElementById('refreshBtn').addEventListener('click', () => {
            log('Refreshing page...');
            window.location.reload();
        });
        
        // Initialize
        checkAuth();
    </script>
</body>
</html>
