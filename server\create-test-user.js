/**
 * Create Test User for Performance Testing
 * إنشاء مستخدم اختبار لاختبارات الأداء
 */

const bcrypt = require('bcrypt');
const { prisma } = require('./config/prisma');

async function createTestUser() {
  try {
    console.log('🔧 إنشاء مستخدم اختبار...');

    // Hash password
    const hashedPassword = await bcrypt.hash('testpass123', 10);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('👤 المستخدم موجود بالفعل، سيتم تحديث كلمة المرور...');
      
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { 
          password: hashedPassword,
          isActive: true
        }
      });
    } else {
      console.log('👤 إنشاء مستخدم جديد...');
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          isActive: true
        }
      });
    }

    console.log('✅ تم إنشاء/تحديث المستخدم بنجاح');
    console.log('📧 البريد الإلكتروني: <EMAIL>');
    console.log('🔑 كلمة المرور: testpass123');

  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
