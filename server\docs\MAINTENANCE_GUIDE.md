# 🔧 دليل الصيانة والمراقبة - WebCore Authentication System

## نظرة عامة

هذا الدليل يوضح كيفية صيانة ومراقبة نظام المصادقة في WebCore للحفاظ على الأداء الأمثل والأمان العالي.

## 📊 مراقبة الأداء

### 1. مؤشرات الأداء الرئيسية (KPIs)

#### أوقات الاستجابة المستهدفة
```javascript
// الحدود المقبولة حسب القوانين الذهبية
const performanceTargets = {
  login: 200,              // تسجيل الدخول < 200ms
  tokenVerification: 50,   // التحقق من التوكن < 50ms
  databaseQuery: 50,       // استعلامات قاعدة البيانات < 50ms
  apiResponse: 200,        // استجابة API < 200ms
  healthCheck: 10          // فحص الصحة < 10ms
};
```

#### استخدام الموارد
```javascript
// الحدود المقبولة للموارد
const resourceLimits = {
  memory: 500,             // الذاكرة < 500MB
  cpu: 70,                 // المعالج < 70%
  connections: 20,         // اتصالات قاعدة البيانات < 20
  cacheHitRate: 80,        // معدل نجاح التخزين المؤقت > 80%
  errorRate: 0.1           // معدل الأخطاء < 0.1%
};
```

### 2. أدوات المراقبة

#### مراقبة الأداء في الوقت الفعلي
```javascript
// فحص مقاييس الأداء
GET /api/metrics/performance

// مراقبة صحة النظام
GET /api/metrics/health

// مقاييس المصادقة
GET /api/metrics/auth
```

#### تنبيهات تلقائية
```javascript
// إعداد التنبيهات
const alerts = {
  highResponseTime: {
    threshold: 500,        // > 500ms
    action: 'ALERT_ADMIN'
  },
  highMemoryUsage: {
    threshold: 400,        // > 400MB
    action: 'SCALE_UP'
  },
  highErrorRate: {
    threshold: 5,          // > 5%
    action: 'EMERGENCY_ALERT'
  }
};
```

## 🧹 مهام الصيانة الدورية

### 1. تنظيف قاعدة البيانات

#### تنظيف التوكنات المنتهية الصلاحية
```javascript
// تشغيل يومي في 2:00 صباحاً
// cron: 0 2 * * *

const cleanupExpiredTokens = async () => {
  const expiredTokens = await prisma.blacklistedToken.deleteMany({
    where: {
      expiresAt: {
        lt: new Date()
      }
    }
  });
  
  console.log(`🧹 تم حذف ${expiredTokens.count} توكن منتهي الصلاحية`);
};
```

#### تنظيف سجلات التدقيق القديمة
```javascript
// تشغيل أسبوعي - الاحتفاظ بـ 90 يوم فقط
const cleanupOldAuditLogs = async () => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - 90);
  
  const deletedLogs = await prisma.auditLog.deleteMany({
    where: {
      createdAt: {
        lt: cutoffDate
      }
    }
  });
  
  console.log(`🧹 تم حذف ${deletedLogs.count} سجل تدقيق قديم`);
};
```

### 2. تحسين قاعدة البيانات

#### إعادة فهرسة الجداول
```sql
-- تشغيل شهري
REINDEX TABLE users;
REINDEX TABLE blacklisted_tokens;
REINDEX TABLE audit_logs;
REINDEX TABLE user_roles;

-- تحليل الإحصائيات
ANALYZE users;
ANALYZE blacklisted_tokens;
ANALYZE audit_logs;
```

#### فحص الاستعلامات البطيئة
```javascript
// مراقبة الاستعلامات > 100ms
const monitorSlowQueries = () => {
  prisma.$use(async (params, next) => {
    const before = Date.now();
    const result = await next(params);
    const after = Date.now();
    
    if (after - before > 100) {
      console.warn(`🐌 استعلام بطيء: ${params.model}.${params.action} - ${after - before}ms`);
      
      // إرسال تنبيه للمطورين
      await sendSlowQueryAlert({
        model: params.model,
        action: params.action,
        duration: after - before,
        timestamp: new Date()
      });
    }
    
    return result;
  });
};
```

## 🔄 النسخ الاحتياطية

### 1. استراتيجية النسخ الاحتياطية

#### نسخ احتياطية يومية
```bash
#!/bin/bash
# backup-daily.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/daily"
DB_NAME="webcore"

# نسخة احتياطية لقاعدة البيانات
pg_dump $DB_NAME > "$BACKUP_DIR/webcore_$DATE.sql"

# ضغط النسخة الاحتياطية
gzip "$BACKUP_DIR/webcore_$DATE.sql"

# حذف النسخ الاحتياطية الأقدم من 7 أيام
find $BACKUP_DIR -name "*.gz" -mtime +7 -delete

echo "✅ تمت النسخة الاحتياطية: webcore_$DATE.sql.gz"
```

#### نسخ احتياطية أسبوعية
```bash
#!/bin/bash
# backup-weekly.sh

DATE=$(date +%Y%m%d)
BACKUP_DIR="/backups/weekly"

# نسخة احتياطية كاملة
pg_dump --verbose --clean --no-acl --no-owner webcore > "$BACKUP_DIR/webcore_weekly_$DATE.sql"

# رفع إلى التخزين السحابي
aws s3 cp "$BACKUP_DIR/webcore_weekly_$DATE.sql" s3://webcore-backups/weekly/

echo "✅ تمت النسخة الاحتياطية الأسبوعية ورفعها للسحابة"
```

### 2. اختبار الاستعادة

#### اختبار شهري للاستعادة
```bash
#!/bin/bash
# test-restore.sh

# إنشاء قاعدة بيانات اختبار
createdb webcore_test

# استعادة من النسخة الاحتياطية
psql webcore_test < /backups/latest/webcore_latest.sql

# اختبار سلامة البيانات
node scripts/test-data-integrity.js --database=webcore_test

# حذف قاعدة البيانات الاختبارية
dropdb webcore_test

echo "✅ تم اختبار الاستعادة بنجاح"
```

## 🔐 صيانة الأمان

### 1. تحديث التبعيات

#### فحص الثغرات الأمنية
```bash
# فحص يومي للثغرات
npm audit

# فحص متقدم مع Snyk
npx snyk test

# تحديث التبعيات الآمنة
npm update

# تحديث التبعيات مع الثغرات
npm audit fix
```

#### مراجعة دورية للتبعيات
```javascript
// package-security-check.js
const { execSync } = require('child_process');

const checkSecurityUpdates = () => {
  try {
    // فحص التحديثات الأمنية
    const auditResult = execSync('npm audit --json', { encoding: 'utf8' });
    const audit = JSON.parse(auditResult);
    
    if (audit.metadata.vulnerabilities.total > 0) {
      console.warn(`⚠️  تم العثور على ${audit.metadata.vulnerabilities.total} ثغرة أمنية`);
      
      // إرسال تنبيه
      sendSecurityAlert(audit);
    } else {
      console.log('✅ لا توجد ثغرات أمنية معروفة');
    }
  } catch (error) {
    console.error('❌ خطأ في فحص الأمان:', error.message);
  }
};
```

### 2. مراجعة السجلات الأمنية

#### تحليل يومي للسجلات
```javascript
// security-log-analysis.js
const analyzeSecurityLogs = async () => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  // فحص محاولات الدخول الفاشلة
  const failedLogins = await prisma.auditLog.count({
    where: {
      action: 'FAILED_LOGIN',
      createdAt: { gte: today }
    }
  });
  
  // فحص الأنشطة المشبوهة
  const suspiciousActivities = await prisma.auditLog.count({
    where: {
      action: 'SUSPICIOUS_ACTIVITY',
      createdAt: { gte: today }
    }
  });
  
  // تقرير يومي
  console.log(`📊 تقرير الأمان اليومي:`);
  console.log(`   - محاولات دخول فاشلة: ${failedLogins}`);
  console.log(`   - أنشطة مشبوهة: ${suspiciousActivities}`);
  
  // تنبيه إذا تجاوزت الحدود
  if (failedLogins > 50 || suspiciousActivities > 10) {
    await sendSecurityAlert({
      type: 'HIGH_SECURITY_ACTIVITY',
      failedLogins,
      suspiciousActivities,
      date: today
    });
  }
};
```

## 📈 تحسين الأداء

### 1. مراقبة الذاكرة المؤقتة

#### إحصائيات التخزين المؤقت
```javascript
// cache-monitoring.js
const monitorCachePerformance = () => {
  const cacheStats = {
    hits: 0,
    misses: 0,
    size: userCache.size,
    hitRate: 0
  };
  
  // حساب معدل النجاح
  cacheStats.hitRate = (cacheStats.hits / (cacheStats.hits + cacheStats.misses)) * 100;
  
  console.log(`📊 إحصائيات التخزين المؤقت:`);
  console.log(`   - معدل النجاح: ${cacheStats.hitRate.toFixed(2)}%`);
  console.log(`   - حجم الذاكرة المؤقتة: ${cacheStats.size} عنصر`);
  
  // تنظيف إذا تجاوز الحد
  if (cacheStats.size > 1000) {
    cleanupExpiredCacheEntries();
  }
};
```

### 2. تحسين قاعدة البيانات

#### مراقبة الاتصالات
```javascript
// database-monitoring.js
const monitorDatabaseConnections = async () => {
  const activeConnections = await prisma.$queryRaw`
    SELECT count(*) as active_connections 
    FROM pg_stat_activity 
    WHERE state = 'active'
  `;
  
  console.log(`🔗 الاتصالات النشطة: ${activeConnections[0].active_connections}`);
  
  if (activeConnections[0].active_connections > 15) {
    console.warn('⚠️  عدد الاتصالات مرتفع - قد تحتاج لتحسين');
  }
};
```

## 🚨 خطة الطوارئ

### 1. إجراءات الطوارئ

#### إيقاف النظام في حالة الطوارئ
```javascript
// emergency-shutdown.js
const emergencyShutdown = async (reason) => {
  console.log(`🚨 إيقاف طارئ: ${reason}`);
  
  // إلغاء جميع التوكنات النشطة
  await prisma.blacklistedToken.createMany({
    data: activeTokens.map(token => ({
      token: token.jti,
      reason: 'EMERGENCY_SHUTDOWN',
      expiresAt: new Date(token.exp * 1000)
    }))
  });
  
  // إرسال تنبيه للمطورين
  await sendEmergencyAlert(reason);
  
  // إيقاف الخادم
  process.exit(1);
};
```

### 2. خطة الاستعادة

#### استعادة سريعة
```bash
#!/bin/bash
# quick-recovery.sh

echo "🔄 بدء الاستعادة السريعة..."

# استعادة من النسخة الاحتياطية الأحدث
psql webcore < /backups/latest/webcore_latest.sql

# إعادة تشغيل الخدمات
systemctl restart webcore-api
systemctl restart nginx

# فحص الصحة
curl -f http://localhost:3000/api/metrics/health

echo "✅ تمت الاستعادة بنجاح"
```

## 📋 قوائم التحقق

### 1. قائمة التحقق اليومية
- [ ] فحص مقاييس الأداء
- [ ] مراجعة سجلات الأخطاء
- [ ] فحص استخدام الموارد
- [ ] تحليل سجلات الأمان
- [ ] تنظيف التوكنات المنتهية الصلاحية

### 2. قائمة التحقق الأسبوعية
- [ ] نسخة احتياطية كاملة
- [ ] فحص الثغرات الأمنية
- [ ] تحديث التبعيات
- [ ] مراجعة الأداء الأسبوعي
- [ ] تنظيف سجلات التدقيق القديمة

### 3. قائمة التحقق الشهرية
- [ ] اختبار الاستعادة من النسخ الاحتياطية
- [ ] مراجعة شاملة للأمان
- [ ] تحسين قاعدة البيانات
- [ ] مراجعة خطة الطوارئ
- [ ] تدريب الفريق على الإجراءات

---

**تاريخ آخر تحديث**: 27 يوليو 2025  
**مستوى الصيانة**: 🔧 متقدم  
**حالة المراقبة**: ✅ نشطة ومستمرة
