<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1753575888146" clover="3.2.0">
  <project timestamp="1753575888146" name="All files">
    <metrics statements="626" coveredstatements="179" conditionals="207" coveredconditionals="55" methods="91" coveredmethods="22" elements="924" coveredelements="256" complexity="0" loc="626" ncloc="626" packages="3" files="13" classes="13"/>
    <package name="routes">
      <metrics statements="267" coveredstatements="100" conditionals="50" coveredconditionals="10" methods="31" coveredmethods="6"/>
      <file name="authRoutes.js" path="C:\my-projact\WebCore\server\routes\authRoutes.js">
        <metrics statements="54" coveredstatements="21" conditionals="18" coveredconditionals="4" methods="6" coveredmethods="1"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="47" count="2" type="stmt"/>
        <line num="48" count="48" type="stmt"/>
        <line num="49" count="48" type="stmt"/>
        <line num="51" count="48" type="cond" truecount="3" falsecount="1"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="48" type="stmt"/>
        <line num="57" count="48" type="cond" truecount="1" falsecount="1"/>
        <line num="58" count="48" type="stmt"/>
        <line num="59" count="48" type="stmt"/>
        <line num="61" count="48" type="stmt"/>
        <line num="62" count="48" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="100" count="2" type="stmt"/>
        <line num="101" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="146" count="2" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="207" count="2" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="211" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="225" count="0" type="stmt"/>
        <line num="231" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="232" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="271" count="2" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="275" count="2" type="stmt"/>
      </file>
      <file name="dashboardRoutes.js" path="C:\my-projact\WebCore\server\routes\dashboardRoutes.js">
        <metrics statements="25" coveredstatements="7" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="64" count="2" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="138" count="2" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="142" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="163" count="2" type="stmt"/>
      </file>
      <file name="index.js" path="C:\my-projact\WebCore\server\routes\index.js">
        <metrics statements="7" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
      </file>
      <file name="moduleRoutes.js" path="C:\my-projact\WebCore\server\routes\moduleRoutes.js">
        <metrics statements="16" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="7" count="2" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="32" count="2" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="48" count="2" type="stmt"/>
      </file>
      <file name="roleRoutes.js" path="C:\my-projact\WebCore\server\routes\roleRoutes.js">
        <metrics statements="51" coveredstatements="35" conditionals="12" coveredconditionals="6" methods="5" coveredmethods="5"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="33" count="2" type="stmt"/>
        <line num="34" count="2" type="stmt"/>
        <line num="35" count="2" type="stmt"/>
        <line num="38" count="2" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="83" count="2" type="stmt"/>
        <line num="84" count="14" type="stmt"/>
        <line num="85" count="14" type="stmt"/>
        <line num="87" count="14" type="cond" truecount="4" falsecount="0"/>
        <line num="88" count="5" type="stmt"/>
        <line num="91" count="9" type="stmt"/>
        <line num="99" count="9" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="103" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="144" count="2" type="stmt"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="147" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="158" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="184" count="2" type="stmt"/>
        <line num="185" count="3" type="stmt"/>
        <line num="186" count="3" type="stmt"/>
        <line num="189" count="3" type="stmt"/>
        <line num="193" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="194" count="1" type="stmt"/>
        <line num="199" count="2" type="stmt"/>
        <line num="203" count="2" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="245" count="2" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="248" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="393" count="0" type="stmt"/>
        <line num="394" count="0" type="stmt"/>
        <line num="398" count="2" type="stmt"/>
      </file>
      <file name="settingsRoutes.js" path="C:\my-projact\WebCore\server\routes\settingsRoutes.js">
        <metrics statements="65" coveredstatements="14" conditionals="8" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="5" count="2" type="stmt"/>
        <line num="130" count="2" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="182" count="2" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="236" count="2" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="240" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="241" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="301" count="2" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="383" count="2" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="388" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="389" count="0" type="stmt"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="438" count="2" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="441" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="444" count="0" type="stmt"/>
        <line num="495" count="2" type="stmt"/>
        <line num="496" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="507" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="557" count="2" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="649" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="662" count="2" type="stmt"/>
      </file>
      <file name="userRoutes.js" path="C:\my-projact\WebCore\server\routes\userRoutes.js">
        <metrics statements="49" coveredstatements="11" conditionals="10" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="39" count="2" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="137" count="2" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="142" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="195" count="2" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="243" count="2" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="248" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="259" count="2" type="stmt"/>
      </file>
    </package>
    <package name="routes.middleware">
      <metrics statements="66" coveredstatements="57" conditionals="52" coveredconditionals="41" methods="12" coveredmethods="12"/>
      <file name="roleCheck.js" path="C:\my-projact\WebCore\server\routes\middleware\roleCheck.js">
        <metrics statements="66" coveredstatements="57" conditionals="52" coveredconditionals="41" methods="12" coveredmethods="12"/>
        <line num="1" count="5" type="stmt"/>
        <line num="10" count="11" type="stmt"/>
        <line num="13" count="11" type="cond" truecount="4" falsecount="0"/>
        <line num="14" count="6" type="stmt"/>
        <line num="15" count="6" type="stmt"/>
        <line num="16" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="18" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="20" count="4" type="stmt"/>
        <line num="22" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="23" count="4" type="stmt"/>
        <line num="27" count="11" type="stmt"/>
        <line num="37" count="55" type="cond" truecount="2" falsecount="0"/>
        <line num="38" count="2" type="stmt"/>
        <line num="42" count="2" type="stmt"/>
        <line num="44" count="53" type="stmt"/>
        <line num="51" count="5" type="stmt"/>
        <line num="52" count="29" type="stmt"/>
        <line num="53" count="13" type="stmt"/>
        <line num="54" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="57" count="11" type="stmt"/>
        <line num="59" count="11" type="cond" truecount="3" falsecount="1"/>
        <line num="60" count="0" type="stmt"/>
        <line num="66" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="67" count="2" type="stmt"/>
        <line num="73" count="10" type="stmt"/>
        <line num="75" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="76" count="4" type="stmt"/>
        <line num="82" count="5" type="stmt"/>
        <line num="83" count="5" type="stmt"/>
        <line num="84" count="5" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="101" count="42" type="cond" truecount="4" falsecount="0"/>
        <line num="102" count="39" type="stmt"/>
        <line num="105" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="110" count="3" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="124" count="42" type="cond" truecount="3" falsecount="1"/>
        <line num="125" count="0" type="stmt"/>
        <line num="128" count="42" type="stmt"/>
        <line num="129" count="42" type="cond" truecount="1" falsecount="1"/>
        <line num="130" count="42" type="stmt"/>
        <line num="132" count="42" type="cond" truecount="6" falsecount="0"/>
        <line num="135" count="26" type="stmt"/>
        <line num="139" count="16" type="stmt"/>
        <line num="147" count="5" type="stmt"/>
        <line num="148" count="31" type="stmt"/>
        <line num="149" count="42" type="stmt"/>
        <line num="150" count="42" type="cond" truecount="1" falsecount="1"/>
        <line num="152" count="42" type="stmt"/>
        <line num="154" count="42" type="cond" truecount="1" falsecount="1"/>
        <line num="155" count="0" type="stmt"/>
        <line num="161" count="42" type="cond" truecount="2" falsecount="0"/>
        <line num="162" count="16" type="stmt"/>
        <line num="168" count="26" type="stmt"/>
        <line num="169" count="26" type="cond" truecount="1" falsecount="1"/>
        <line num="171" count="26" type="stmt"/>
        <line num="172" count="26" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="186" count="5" type="stmt"/>
        <line num="191" count="5" type="stmt"/>
        <line num="193" count="5" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="293" coveredstatements="22" conditionals="105" coveredconditionals="4" methods="48" coveredmethods="4"/>
      <file name="ModuleService.js" path="C:\my-projact\WebCore\server\services\ModuleService.js">
        <metrics statements="96" coveredstatements="0" conditionals="45" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="190" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="191" count="0" type="stmt"/>
        <line num="195" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="246" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="351" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="353" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="357" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
      </file>
      <file name="SettingsService.js" path="C:\my-projact\WebCore\server\services\SettingsService.js">
        <metrics statements="58" coveredstatements="3" conditionals="13" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="140" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="210" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="234" count="0" type="stmt"/>
        <line num="238" count="2" type="stmt"/>
      </file>
      <file name="llmService.js" path="C:\my-projact\WebCore\server\services\llmService.js">
        <metrics statements="31" coveredstatements="0" conditionals="7" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="62" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="prismaUserService.js" path="C:\my-projact\WebCore\server\services\prismaUserService.js">
        <metrics statements="60" coveredstatements="19" conditionals="21" coveredconditionals="4" methods="12" coveredmethods="4"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="3" count="5" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="28" count="32" type="stmt"/>
        <line num="29" count="32" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="48" type="cond" truecount="1" falsecount="1"/>
        <line num="105" count="48" type="cond" truecount="1" falsecount="1"/>
        <line num="107" count="48" type="stmt"/>
        <line num="108" count="48" type="stmt"/>
        <line num="119" count="48" type="cond" truecount="1" falsecount="1"/>
        <line num="121" count="48" type="stmt"/>
        <line num="122" count="48" type="cond" truecount="1" falsecount="1"/>
        <line num="125" count="48" type="stmt"/>
        <line num="143" count="48" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="156" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="204" count="48" type="stmt"/>
        <line num="205" count="48" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="236" count="7" type="stmt"/>
        <line num="237" count="7" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="260" count="5" type="stmt"/>
      </file>
      <file name="userService.js" path="C:\my-projact\WebCore\server\services\userService.js">
        <metrics statements="48" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="8" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="69" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
