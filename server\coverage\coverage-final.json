{"C:\\my-projact\\WebCore\\server\\routes\\authRoutes.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\authRoutes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 26}, "end": {"line": 2, "column": 69}}, "2": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 55}}, "3": {"start": {"line": 4, "column": 54}, "end": {"line": 4, "column": 81}}, "4": {"start": {"line": 5, "column": 12}, "end": {"line": 5, "column": 35}}, "5": {"start": {"line": 7, "column": 15}, "end": {"line": 7, "column": 31}}, "6": {"start": {"line": 47, "column": 0}, "end": {"line": 67, "column": 3}}, "7": {"start": {"line": 48, "column": 20}, "end": {"line": 48, "column": 65}}, "8": {"start": {"line": 48, "column": 27}, "end": {"line": 48, "column": 65}}, "9": {"start": {"line": 49, "column": 30}, "end": {"line": 49, "column": 38}}, "10": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 3}}, "11": {"start": {"line": 52, "column": 4}, "end": {"line": 52, "column": 56}}, "12": {"start": {"line": 55, "column": 15}, "end": {"line": 55, "column": 80}}, "13": {"start": {"line": 57, "column": 2}, "end": {"line": 66, "column": 3}}, "14": {"start": {"line": 58, "column": 24}, "end": {"line": 58, "column": 49}}, "15": {"start": {"line": 59, "column": 25}, "end": {"line": 59, "column": 51}}, "16": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": 70}}, "17": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 58}}, "18": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 55}}, "19": {"start": {"line": 100, "column": 0}, "end": {"line": 111, "column": 3}}, "20": {"start": {"line": 101, "column": 2}, "end": {"line": 103, "column": 3}}, "21": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 40}}, "22": {"start": {"line": 104, "column": 2}, "end": {"line": 110, "column": 3}}, "23": {"start": {"line": 105, "column": 17}, "end": {"line": 105, "column": 57}}, "24": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 38}}, "25": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 60}}, "26": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 43}}, "27": {"start": {"line": 146, "column": 0}, "end": {"line": 155, "column": 3}}, "28": {"start": {"line": 147, "column": 20}, "end": {"line": 147, "column": 28}}, "29": {"start": {"line": 149, "column": 15}, "end": {"line": 149, "column": 56}}, "30": {"start": {"line": 150, "column": 2}, "end": {"line": 152, "column": 3}}, "31": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 62}}, "32": {"start": {"line": 154, "column": 2}, "end": {"line": 154, "column": 69}}, "33": {"start": {"line": 207, "column": 0}, "end": {"line": 269, "column": 3}}, "34": {"start": {"line": 208, "column": 27}, "end": {"line": 208, "column": 35}}, "35": {"start": {"line": 210, "column": 2}, "end": {"line": 215, "column": 3}}, "36": {"start": {"line": 211, "column": 4}, "end": {"line": 214, "column": 7}}, "37": {"start": {"line": 217, "column": 2}, "end": {"line": 268, "column": 3}}, "38": {"start": {"line": 219, "column": 20}, "end": {"line": 219, "column": 78}}, "39": {"start": {"line": 222, "column": 17}, "end": {"line": 222, "column": 57}}, "40": {"start": {"line": 224, "column": 4}, "end": {"line": 229, "column": 5}}, "41": {"start": {"line": 225, "column": 6}, "end": {"line": 228, "column": 9}}, "42": {"start": {"line": 231, "column": 4}, "end": {"line": 236, "column": 5}}, "43": {"start": {"line": 232, "column": 6}, "end": {"line": 235, "column": 9}}, "44": {"start": {"line": 239, "column": 27}, "end": {"line": 239, "column": 52}}, "45": {"start": {"line": 240, "column": 28}, "end": {"line": 240, "column": 54}}, "46": {"start": {"line": 243, "column": 4}, "end": {"line": 243, "column": 73}}, "47": {"start": {"line": 246, "column": 4}, "end": {"line": 252, "column": 7}}, "48": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 59}}, "49": {"start": {"line": 257, "column": 4}, "end": {"line": 262, "column": 5}}, "50": {"start": {"line": 258, "column": 6}, "end": {"line": 261, "column": 9}}, "51": {"start": {"line": 264, "column": 4}, "end": {"line": 267, "column": 7}}, "52": {"start": {"line": 271, "column": 0}, "end": {"line": 273, "column": 3}}, "53": {"start": {"line": 272, "column": 2}, "end": {"line": 272, "column": 40}}, "54": {"start": {"line": 275, "column": 0}, "end": {"line": 275, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 47, "column": 22}, "end": {"line": 47, "column": 23}}, "loc": {"start": {"line": 47, "column": 42}, "end": {"line": 67, "column": 1}}, "line": 47}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 48, "column": 20}, "end": {"line": 48, "column": 21}}, "loc": {"start": {"line": 48, "column": 27}, "end": {"line": 48, "column": 65}}, "line": 48}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 100, "column": 25}, "end": {"line": 100, "column": 26}}, "loc": {"start": {"line": 100, "column": 51}, "end": {"line": 111, "column": 1}}, "line": 100}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 146, "column": 23}, "end": {"line": 146, "column": 24}}, "loc": {"start": {"line": 146, "column": 43}, "end": {"line": 155, "column": 1}}, "line": 146}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 207, "column": 24}, "end": {"line": 207, "column": 25}}, "loc": {"start": {"line": 207, "column": 44}, "end": {"line": 269, "column": 1}}, "line": 207}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 271, "column": 31}, "end": {"line": 271, "column": 32}}, "loc": {"start": {"line": 271, "column": 51}, "end": {"line": 273, "column": 1}}, "line": 271}}, "branchMap": {"0": {"loc": {"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 3}}, "type": "if", "locations": [{"start": {"line": 51, "column": 2}, "end": {"line": 53, "column": 3}}, {"start": {}, "end": {}}], "line": 51}, "1": {"loc": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 25}}, "type": "binary-expr", "locations": [{"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 12}}, {"start": {"line": 51, "column": 16}, "end": {"line": 51, "column": 25}}], "line": 51}, "2": {"loc": {"start": {"line": 57, "column": 2}, "end": {"line": 66, "column": 3}}, "type": "if", "locations": [{"start": {"line": 57, "column": 2}, "end": {"line": 66, "column": 3}}, {"start": {"line": 63, "column": 9}, "end": {"line": 66, "column": 3}}], "line": 57}, "3": {"loc": {"start": {"line": 101, "column": 2}, "end": {"line": 103, "column": 3}}, "type": "if", "locations": [{"start": {"line": 101, "column": 2}, "end": {"line": 103, "column": 3}}, {"start": {}, "end": {}}], "line": 101}, "4": {"loc": {"start": {"line": 150, "column": 2}, "end": {"line": 152, "column": 3}}, "type": "if", "locations": [{"start": {"line": 150, "column": 2}, "end": {"line": 152, "column": 3}}, {"start": {}, "end": {}}], "line": 150}, "5": {"loc": {"start": {"line": 210, "column": 2}, "end": {"line": 215, "column": 3}}, "type": "if", "locations": [{"start": {"line": 210, "column": 2}, "end": {"line": 215, "column": 3}}, {"start": {}, "end": {}}], "line": 210}, "6": {"loc": {"start": {"line": 224, "column": 4}, "end": {"line": 229, "column": 5}}, "type": "if", "locations": [{"start": {"line": 224, "column": 4}, "end": {"line": 229, "column": 5}}, {"start": {}, "end": {}}], "line": 224}, "7": {"loc": {"start": {"line": 231, "column": 4}, "end": {"line": 236, "column": 5}}, "type": "if", "locations": [{"start": {"line": 231, "column": 4}, "end": {"line": 236, "column": 5}}, {"start": {}, "end": {}}], "line": 231}, "8": {"loc": {"start": {"line": 257, "column": 4}, "end": {"line": 262, "column": 5}}, "type": "if", "locations": [{"start": {"line": 257, "column": 4}, "end": {"line": 262, "column": 5}}, {"start": {}, "end": {}}], "line": 257}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 48, "8": 0, "9": 48, "10": 48, "11": 0, "12": 48, "13": 48, "14": 48, "15": 48, "16": 48, "17": 48, "18": 0, "19": 2, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 2, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 2, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 2, "53": 0, "54": 2}, "f": {"0": 48, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0, 48], "1": [48, 48], "2": [48, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "3bc88b0129f2e0a13a63d70b8ce0863cbe415bbe"}, "C:\\my-projact\\WebCore\\server\\routes\\dashboardRoutes.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\dashboardRoutes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 55}}, "3": {"start": {"line": 5, "column": 15}, "end": {"line": 5, "column": 31}}, "4": {"start": {"line": 64, "column": 0}, "end": {"line": 111, "column": 3}}, "5": {"start": {"line": 65, "column": 2}, "end": {"line": 110, "column": 3}}, "6": {"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": 48}}, "7": {"start": {"line": 70, "column": 24}, "end": {"line": 72, "column": 6}}, "8": {"start": {"line": 75, "column": 23}, "end": {"line": 75, "column": 48}}, "9": {"start": {"line": 78, "column": 25}, "end": {"line": 78, "column": 52}}, "10": {"start": {"line": 81, "column": 22}, "end": {"line": 81, "column": 32}}, "11": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 47}}, "12": {"start": {"line": 84, "column": 25}, "end": {"line": 90, "column": 6}}, "13": {"start": {"line": 93, "column": 25}, "end": {"line": 96, "column": 5}}, "14": {"start": {"line": 98, "column": 4}, "end": {"line": 105, "column": 7}}, "15": {"start": {"line": 108, "column": 4}, "end": {"line": 108, "column": 60}}, "16": {"start": {"line": 109, "column": 4}, "end": {"line": 109, "column": 76}}, "17": {"start": {"line": 138, "column": 0}, "end": {"line": 161, "column": 3}}, "18": {"start": {"line": 139, "column": 2}, "end": {"line": 160, "column": 3}}, "19": {"start": {"line": 140, "column": 18}, "end": {"line": 140, "column": 49}}, "20": {"start": {"line": 142, "column": 27}, "end": {"line": 153, "column": 6}}, "21": {"start": {"line": 155, "column": 4}, "end": {"line": 155, "column": 29}}, "22": {"start": {"line": 158, "column": 4}, "end": {"line": 158, "column": 60}}, "23": {"start": {"line": 159, "column": 4}, "end": {"line": 159, "column": 71}}, "24": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 64, "column": 34}, "end": {"line": 64, "column": 35}}, "loc": {"start": {"line": 64, "column": 54}, "end": {"line": 111, "column": 1}}, "line": 64}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 138, "column": 44}, "end": {"line": 138, "column": 45}}, "loc": {"start": {"line": 138, "column": 64}, "end": {"line": 161, "column": 1}}, "line": 138}}, "branchMap": {"0": {"loc": {"start": {"line": 140, "column": 18}, "end": {"line": 140, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 140, "column": 18}, "end": {"line": 140, "column": 43}}, {"start": {"line": 140, "column": 47}, "end": {"line": 140, "column": 49}}], "line": 140}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 2, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 2}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a289ffa2b6804081cff07007e37e0b05868a6480"}, "C:\\my-projact\\WebCore\\server\\routes\\index.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\index.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 5, "column": 0}, "end": {"line": 7, "column": 3}}, "3": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 51}}, "4": {"start": {"line": 9, "column": 0}, "end": {"line": 11, "column": 3}}, "5": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 31}}, "6": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 16}, "end": {"line": 5, "column": 17}}, "loc": {"start": {"line": 5, "column": 30}, "end": {"line": 7, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 9, "column": 20}, "end": {"line": 9, "column": 21}}, "loc": {"start": {"line": 9, "column": 34}, "end": {"line": 11, "column": 1}}, "line": 9}}, "branchMap": {}, "s": {"0": 2, "1": 2, "2": 2, "3": 0, "4": 2, "5": 0, "6": 2}, "f": {"0": 0, "1": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "dadc6cdb6c9432cbfd924cb86b9303630d8abc0d"}, "C:\\my-projact\\WebCore\\server\\routes\\moduleRoutes.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\moduleRoutes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 24}, "end": {"line": 2, "column": 55}}, "2": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 31}}, "3": {"start": {"line": 7, "column": 0}, "end": {"line": 12, "column": 3}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 11, "column": 5}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 29, "column": 3}}, "6": {"start": {"line": 16, "column": 2}, "end": {"line": 28, "column": 3}}, "7": {"start": {"line": 18, "column": 4}, "end": {"line": 21, "column": 7}}, "8": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": 63}}, "9": {"start": {"line": 24, "column": 4}, "end": {"line": 27, "column": 7}}, "10": {"start": {"line": 32, "column": 0}, "end": {"line": 46, "column": 3}}, "11": {"start": {"line": 33, "column": 2}, "end": {"line": 45, "column": 3}}, "12": {"start": {"line": 35, "column": 4}, "end": {"line": 38, "column": 7}}, "13": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 52}}, "14": {"start": {"line": 41, "column": 4}, "end": {"line": 44, "column": 7}}, "15": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 20}, "end": {"line": 7, "column": 21}}, "loc": {"start": {"line": 7, "column": 34}, "end": {"line": 12, "column": 1}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 39}, "end": {"line": 15, "column": 40}}, "loc": {"start": {"line": 15, "column": 59}, "end": {"line": 29, "column": 1}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 29}, "end": {"line": 32, "column": 30}}, "loc": {"start": {"line": 32, "column": 49}, "end": {"line": 46, "column": 1}}, "line": 32}}, "branchMap": {}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 0, "5": 2, "6": 0, "7": 0, "8": 0, "9": 0, "10": 2, "11": 0, "12": 0, "13": 0, "14": 0, "15": 2}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "c2c95d8212d64849268d56cb72367e4da7fb9cc7"}, "C:\\my-projact\\WebCore\\server\\routes\\roleRoutes.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\roleRoutes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 55}}, "3": {"start": {"line": 4, "column": 53}, "end": {"line": 4, "column": 89}}, "4": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "5": {"start": {"line": 33, "column": 0}, "end": {"line": 43, "column": 3}}, "6": {"start": {"line": 34, "column": 2}, "end": {"line": 42, "column": 3}}, "7": {"start": {"line": 35, "column": 18}, "end": {"line": 37, "column": 6}}, "8": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 20}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 50}}, "10": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 61}}, "11": {"start": {"line": 83, "column": 0}, "end": {"line": 107, "column": 3}}, "12": {"start": {"line": 84, "column": 2}, "end": {"line": 106, "column": 3}}, "13": {"start": {"line": 85, "column": 47}, "end": {"line": 85, "column": 55}}, "14": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "15": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 82}}, "16": {"start": {"line": 91, "column": 17}, "end": {"line": 97, "column": 6}}, "17": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 31}}, "18": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": 49}}, "19": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, "20": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 83}}, "21": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 61}}, "22": {"start": {"line": 144, "column": 0}, "end": {"line": 162, "column": 3}}, "23": {"start": {"line": 145, "column": 2}, "end": {"line": 161, "column": 3}}, "24": {"start": {"line": 146, "column": 19}, "end": {"line": 146, "column": 29}}, "25": {"start": {"line": 147, "column": 23}, "end": {"line": 147, "column": 31}}, "26": {"start": {"line": 149, "column": 17}, "end": {"line": 152, "column": 6}}, "27": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 19}}, "28": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": 49}}, "29": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "30": {"start": {"line": 158, "column": 6}, "end": {"line": 158, "column": 63}}, "31": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": 61}}, "32": {"start": {"line": 184, "column": 0}, "end": {"line": 211, "column": 3}}, "33": {"start": {"line": 185, "column": 2}, "end": {"line": 210, "column": 3}}, "34": {"start": {"line": 186, "column": 19}, "end": {"line": 186, "column": 29}}, "35": {"start": {"line": 189, "column": 22}, "end": {"line": 191, "column": 6}}, "36": {"start": {"line": 193, "column": 4}, "end": {"line": 197, "column": 5}}, "37": {"start": {"line": 194, "column": 6}, "end": {"line": 196, "column": 9}}, "38": {"start": {"line": 199, "column": 4}, "end": {"line": 201, "column": 7}}, "39": {"start": {"line": 203, "column": 4}, "end": {"line": 203, "column": 70}}, "40": {"start": {"line": 205, "column": 4}, "end": {"line": 205, "column": 49}}, "41": {"start": {"line": 206, "column": 4}, "end": {"line": 208, "column": 5}}, "42": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 63}}, "43": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": 61}}, "44": {"start": {"line": 245, "column": 0}, "end": {"line": 396, "column": 3}}, "45": {"start": {"line": 246, "column": 2}, "end": {"line": 395, "column": 3}}, "46": {"start": {"line": 248, "column": 24}, "end": {"line": 389, "column": 5}}, "47": {"start": {"line": 391, "column": 4}, "end": {"line": 391, "column": 26}}, "48": {"start": {"line": 393, "column": 4}, "end": {"line": 393, "column": 56}}, "49": {"start": {"line": 394, "column": 4}, "end": {"line": 394, "column": 67}}, "50": {"start": {"line": 398, "column": 0}, "end": {"line": 398, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 33, "column": 63}, "end": {"line": 33, "column": 64}}, "loc": {"start": {"line": 33, "column": 83}, "end": {"line": 43, "column": 1}}, "line": 33}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 83, "column": 66}, "end": {"line": 83, "column": 67}}, "loc": {"start": {"line": 83, "column": 86}, "end": {"line": 107, "column": 1}}, "line": 83}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 144, "column": 68}, "end": {"line": 144, "column": 69}}, "loc": {"start": {"line": 144, "column": 88}, "end": {"line": 162, "column": 1}}, "line": 144}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 184, "column": 71}, "end": {"line": 184, "column": 72}}, "loc": {"start": {"line": 184, "column": 91}, "end": {"line": 211, "column": 1}}, "line": 184}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 245, "column": 74}, "end": {"line": 245, "column": 75}}, "loc": {"start": {"line": 245, "column": 94}, "end": {"line": 396, "column": 1}}, "line": 245}}, "branchMap": {"0": {"loc": {"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, "type": "if", "locations": [{"start": {"line": 87, "column": 4}, "end": {"line": 89, "column": 5}}, {"start": {}, "end": {}}], "line": 87}, "1": {"loc": {"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 29}}, "type": "binary-expr", "locations": [{"start": {"line": 87, "column": 8}, "end": {"line": 87, "column": 13}}, {"start": {"line": 87, "column": 17}, "end": {"line": 87, "column": 29}}], "line": 87}, "2": {"loc": {"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, "type": "if", "locations": [{"start": {"line": 102, "column": 4}, "end": {"line": 104, "column": 5}}, {"start": {}, "end": {}}], "line": 102}, "3": {"loc": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, "type": "if", "locations": [{"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": 5}}, {"start": {}, "end": {}}], "line": 157}, "4": {"loc": {"start": {"line": 193, "column": 4}, "end": {"line": 197, "column": 5}}, "type": "if", "locations": [{"start": {"line": 193, "column": 4}, "end": {"line": 197, "column": 5}}, {"start": {}, "end": {}}], "line": 193}, "5": {"loc": {"start": {"line": 206, "column": 4}, "end": {"line": 208, "column": 5}}, "type": "if", "locations": [{"start": {"line": 206, "column": 4}, "end": {"line": 208, "column": 5}}, {"start": {}, "end": {}}], "line": 206}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 2, "7": 2, "8": 2, "9": 0, "10": 0, "11": 2, "12": 14, "13": 14, "14": 14, "15": 5, "16": 9, "17": 9, "18": 0, "19": 0, "20": 0, "21": 0, "22": 2, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 0, "29": 0, "30": 0, "31": 0, "32": 2, "33": 3, "34": 3, "35": 3, "36": 3, "37": 1, "38": 2, "39": 2, "40": 0, "41": 0, "42": 0, "43": 0, "44": 2, "45": 1, "46": 1, "47": 1, "48": 0, "49": 0, "50": 2}, "f": {"0": 2, "1": 14, "2": 1, "3": 3, "4": 1}, "b": {"0": [5, 9], "1": [14, 11], "2": [0, 0], "3": [0, 0], "4": [1, 2], "5": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a9386f8e5eaefa99bab58d34a74833735f63dadb"}, "C:\\my-projact\\WebCore\\server\\routes\\settingsRoutes.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\settingsRoutes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 62}}, "3": {"start": {"line": 4, "column": 30}, "end": {"line": 4, "column": 58}}, "4": {"start": {"line": 5, "column": 22}, "end": {"line": 5, "column": 55}}, "5": {"start": {"line": 130, "column": 0}, "end": {"line": 144, "column": 3}}, "6": {"start": {"line": 131, "column": 2}, "end": {"line": 143, "column": 3}}, "7": {"start": {"line": 132, "column": 21}, "end": {"line": 132, "column": 59}}, "8": {"start": {"line": 133, "column": 4}, "end": {"line": 136, "column": 7}}, "9": {"start": {"line": 138, "column": 4}, "end": {"line": 138, "column": 52}}, "10": {"start": {"line": 139, "column": 4}, "end": {"line": 142, "column": 7}}, "11": {"start": {"line": 182, "column": 0}, "end": {"line": 197, "column": 3}}, "12": {"start": {"line": 183, "column": 2}, "end": {"line": 196, "column": 3}}, "13": {"start": {"line": 184, "column": 25}, "end": {"line": 184, "column": 35}}, "14": {"start": {"line": 185, "column": 21}, "end": {"line": 185, "column": 74}}, "15": {"start": {"line": 186, "column": 4}, "end": {"line": 189, "column": 7}}, "16": {"start": {"line": 191, "column": 4}, "end": {"line": 191, "column": 61}}, "17": {"start": {"line": 192, "column": 4}, "end": {"line": 195, "column": 7}}, "18": {"start": {"line": 236, "column": 0}, "end": {"line": 256, "column": 3}}, "19": {"start": {"line": 237, "column": 2}, "end": {"line": 255, "column": 3}}, "20": {"start": {"line": 238, "column": 25}, "end": {"line": 238, "column": 33}}, "21": {"start": {"line": 240, "column": 4}, "end": {"line": 245, "column": 5}}, "22": {"start": {"line": 241, "column": 6}, "end": {"line": 244, "column": 9}}, "23": {"start": {"line": 247, "column": 19}, "end": {"line": 247, "column": 69}}, "24": {"start": {"line": 248, "column": 4}, "end": {"line": 248, "column": 21}}, "25": {"start": {"line": 250, "column": 4}, "end": {"line": 250, "column": 53}}, "26": {"start": {"line": 251, "column": 4}, "end": {"line": 254, "column": 7}}, "27": {"start": {"line": 301, "column": 0}, "end": {"line": 324, "column": 3}}, "28": {"start": {"line": 302, "column": 2}, "end": {"line": 323, "column": 3}}, "29": {"start": {"line": 303, "column": 30}, "end": {"line": 303, "column": 40}}, "30": {"start": {"line": 304, "column": 18}, "end": {"line": 304, "column": 65}}, "31": {"start": {"line": 306, "column": 4}, "end": {"line": 311, "column": 5}}, "32": {"start": {"line": 307, "column": 6}, "end": {"line": 310, "column": 9}}, "33": {"start": {"line": 313, "column": 4}, "end": {"line": 316, "column": 7}}, "34": {"start": {"line": 318, "column": 4}, "end": {"line": 318, "column": 51}}, "35": {"start": {"line": 319, "column": 4}, "end": {"line": 322, "column": 7}}, "36": {"start": {"line": 383, "column": 0}, "end": {"line": 407, "column": 3}}, "37": {"start": {"line": 384, "column": 2}, "end": {"line": 406, "column": 3}}, "38": {"start": {"line": 385, "column": 30}, "end": {"line": 385, "column": 40}}, "39": {"start": {"line": 386, "column": 35}, "end": {"line": 386, "column": 43}}, "40": {"start": {"line": 388, "column": 4}, "end": {"line": 393, "column": 5}}, "41": {"start": {"line": 389, "column": 6}, "end": {"line": 392, "column": 9}}, "42": {"start": {"line": 395, "column": 4}, "end": {"line": 395, "column": 72}}, "43": {"start": {"line": 396, "column": 4}, "end": {"line": 399, "column": 7}}, "44": {"start": {"line": 401, "column": 4}, "end": {"line": 401, "column": 49}}, "45": {"start": {"line": 402, "column": 4}, "end": {"line": 405, "column": 7}}, "46": {"start": {"line": 438, "column": 0}, "end": {"line": 449, "column": 3}}, "47": {"start": {"line": 439, "column": 2}, "end": {"line": 448, "column": 3}}, "48": {"start": {"line": 440, "column": 19}, "end": {"line": 440, "column": 68}}, "49": {"start": {"line": 441, "column": 4}, "end": {"line": 441, "column": 21}}, "50": {"start": {"line": 443, "column": 4}, "end": {"line": 443, "column": 57}}, "51": {"start": {"line": 444, "column": 4}, "end": {"line": 447, "column": 7}}, "52": {"start": {"line": 495, "column": 0}, "end": {"line": 523, "column": 3}}, "53": {"start": {"line": 496, "column": 2}, "end": {"line": 522, "column": 3}}, "54": {"start": {"line": 498, "column": 27}, "end": {"line": 498, "column": 81}}, "55": {"start": {"line": 500, "column": 4}, "end": {"line": 505, "column": 7}}, "56": {"start": {"line": 507, "column": 4}, "end": {"line": 507, "column": 59}}, "57": {"start": {"line": 509, "column": 4}, "end": {"line": 521, "column": 7}}, "58": {"start": {"line": 557, "column": 0}, "end": {"line": 660, "column": 3}}, "59": {"start": {"line": 558, "column": 2}, "end": {"line": 659, "column": 3}}, "60": {"start": {"line": 560, "column": 22}, "end": {"line": 647, "column": 5}}, "61": {"start": {"line": 649, "column": 4}, "end": {"line": 652, "column": 7}}, "62": {"start": {"line": 654, "column": 4}, "end": {"line": 654, "column": 53}}, "63": {"start": {"line": 655, "column": 4}, "end": {"line": 658, "column": 7}}, "64": {"start": {"line": 662, "column": 0}, "end": {"line": 662, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 130, "column": 57}, "end": {"line": 130, "column": 58}}, "loc": {"start": {"line": 130, "column": 77}, "end": {"line": 144, "column": 1}}, "line": 130}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 182, "column": 75}, "end": {"line": 182, "column": 76}}, "loc": {"start": {"line": 182, "column": 95}, "end": {"line": 197, "column": 1}}, "line": 182}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 236, "column": 57}, "end": {"line": 236, "column": 58}}, "loc": {"start": {"line": 236, "column": 77}, "end": {"line": 256, "column": 1}}, "line": 236}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 301, "column": 71}, "end": {"line": 301, "column": 72}}, "loc": {"start": {"line": 301, "column": 91}, "end": {"line": 324, "column": 1}}, "line": 301}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 383, "column": 71}, "end": {"line": 383, "column": 72}}, "loc": {"start": {"line": 383, "column": 91}, "end": {"line": 407, "column": 1}}, "line": 383}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 438, "column": 68}, "end": {"line": 438, "column": 69}}, "loc": {"start": {"line": 438, "column": 88}, "end": {"line": 449, "column": 1}}, "line": 438}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 495, "column": 22}, "end": {"line": 495, "column": 23}}, "loc": {"start": {"line": 495, "column": 42}, "end": {"line": 523, "column": 1}}, "line": 495}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 557, "column": 25}, "end": {"line": 557, "column": 26}}, "loc": {"start": {"line": 557, "column": 45}, "end": {"line": 660, "column": 1}}, "line": 557}}, "branchMap": {"0": {"loc": {"start": {"line": 240, "column": 4}, "end": {"line": 245, "column": 5}}, "type": "if", "locations": [{"start": {"line": 240, "column": 4}, "end": {"line": 245, "column": 5}}, {"start": {}, "end": {}}], "line": 240}, "1": {"loc": {"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 240, "column": 8}, "end": {"line": 240, "column": 21}}, {"start": {"line": 240, "column": 25}, "end": {"line": 240, "column": 57}}], "line": 240}, "2": {"loc": {"start": {"line": 306, "column": 4}, "end": {"line": 311, "column": 5}}, "type": "if", "locations": [{"start": {"line": 306, "column": 4}, "end": {"line": 311, "column": 5}}, {"start": {}, "end": {}}], "line": 306}, "3": {"loc": {"start": {"line": 388, "column": 4}, "end": {"line": 393, "column": 5}}, "type": "if", "locations": [{"start": {"line": 388, "column": 4}, "end": {"line": 393, "column": 5}}, {"start": {}, "end": {}}], "line": 388}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 2, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 2, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 2, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 2, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 2, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 2, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 2, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 2}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "41713bac721838192878a3ebbe9aebb717c61048"}, "C:\\my-projact\\WebCore\\server\\routes\\userRoutes.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\userRoutes.js", "statementMap": {"0": {"start": {"line": 1, "column": 16}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 26}, "end": {"line": 2, "column": 69}}, "2": {"start": {"line": 3, "column": 24}, "end": {"line": 3, "column": 55}}, "3": {"start": {"line": 4, "column": 28}, "end": {"line": 4, "column": 64}}, "4": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 31}}, "5": {"start": {"line": 39, "column": 0}, "end": {"line": 47, "column": 3}}, "6": {"start": {"line": 40, "column": 2}, "end": {"line": 46, "column": 3}}, "7": {"start": {"line": 41, "column": 18}, "end": {"line": 41, "column": 48}}, "8": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 20}}, "9": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 50}}, "10": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 61}}, "11": {"start": {"line": 80, "column": 0}, "end": {"line": 94, "column": 3}}, "12": {"start": {"line": 81, "column": 2}, "end": {"line": 93, "column": 3}}, "13": {"start": {"line": 82, "column": 19}, "end": {"line": 82, "column": 29}}, "14": {"start": {"line": 83, "column": 17}, "end": {"line": 83, "column": 48}}, "15": {"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 5}}, "16": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 63}}, "17": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 19}}, "18": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 49}}, "19": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 60}}, "20": {"start": {"line": 137, "column": 0}, "end": {"line": 151, "column": 3}}, "21": {"start": {"line": 138, "column": 2}, "end": {"line": 150, "column": 3}}, "22": {"start": {"line": 139, "column": 32}, "end": {"line": 139, "column": 40}}, "23": {"start": {"line": 141, "column": 4}, "end": {"line": 143, "column": 5}}, "24": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": 80}}, "25": {"start": {"line": 145, "column": 17}, "end": {"line": 145, "column": 68}}, "26": {"start": {"line": 146, "column": 4}, "end": {"line": 146, "column": 31}}, "27": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": 49}}, "28": {"start": {"line": 149, "column": 4}, "end": {"line": 149, "column": 51}}, "29": {"start": {"line": 195, "column": 0}, "end": {"line": 209, "column": 3}}, "30": {"start": {"line": 196, "column": 2}, "end": {"line": 208, "column": 3}}, "31": {"start": {"line": 197, "column": 19}, "end": {"line": 197, "column": 29}}, "32": {"start": {"line": 198, "column": 23}, "end": {"line": 198, "column": 31}}, "33": {"start": {"line": 200, "column": 17}, "end": {"line": 200, "column": 63}}, "34": {"start": {"line": 201, "column": 4}, "end": {"line": 201, "column": 19}}, "35": {"start": {"line": 203, "column": 4}, "end": {"line": 203, "column": 49}}, "36": {"start": {"line": 204, "column": 4}, "end": {"line": 206, "column": 5}}, "37": {"start": {"line": 205, "column": 6}, "end": {"line": 205, "column": 63}}, "38": {"start": {"line": 207, "column": 4}, "end": {"line": 207, "column": 51}}, "39": {"start": {"line": 243, "column": 0}, "end": {"line": 257, "column": 3}}, "40": {"start": {"line": 244, "column": 2}, "end": {"line": 256, "column": 3}}, "41": {"start": {"line": 245, "column": 19}, "end": {"line": 245, "column": 29}}, "42": {"start": {"line": 246, "column": 20}, "end": {"line": 246, "column": 54}}, "43": {"start": {"line": 248, "column": 4}, "end": {"line": 250, "column": 5}}, "44": {"start": {"line": 249, "column": 6}, "end": {"line": 249, "column": 63}}, "45": {"start": {"line": 252, "column": 4}, "end": {"line": 252, "column": 70}}, "46": {"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 49}}, "47": {"start": {"line": 255, "column": 4}, "end": {"line": 255, "column": 61}}, "48": {"start": {"line": 259, "column": 0}, "end": {"line": 259, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 39, "column": 63}, "end": {"line": 39, "column": 64}}, "loc": {"start": {"line": 39, "column": 83}, "end": {"line": 47, "column": 1}}, "line": 39}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 80, "column": 32}, "end": {"line": 80, "column": 33}}, "loc": {"start": {"line": 80, "column": 52}, "end": {"line": 94, "column": 1}}, "line": 80}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 137, "column": 66}, "end": {"line": 137, "column": 67}}, "loc": {"start": {"line": 137, "column": 86}, "end": {"line": 151, "column": 1}}, "line": 137}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 195, "column": 32}, "end": {"line": 195, "column": 33}}, "loc": {"start": {"line": 195, "column": 52}, "end": {"line": 209, "column": 1}}, "line": 195}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 243, "column": 35}, "end": {"line": 243, "column": 36}}, "loc": {"start": {"line": 243, "column": 55}, "end": {"line": 257, "column": 1}}, "line": 243}}, "branchMap": {"0": {"loc": {"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 5}}, "type": "if", "locations": [{"start": {"line": 85, "column": 4}, "end": {"line": 87, "column": 5}}, {"start": {}, "end": {}}], "line": 85}, "1": {"loc": {"start": {"line": 141, "column": 4}, "end": {"line": 143, "column": 5}}, "type": "if", "locations": [{"start": {"line": 141, "column": 4}, "end": {"line": 143, "column": 5}}, {"start": {}, "end": {}}], "line": 141}, "2": {"loc": {"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 27}}, "type": "binary-expr", "locations": [{"start": {"line": 141, "column": 8}, "end": {"line": 141, "column": 14}}, {"start": {"line": 141, "column": 18}, "end": {"line": 141, "column": 27}}], "line": 141}, "3": {"loc": {"start": {"line": 204, "column": 4}, "end": {"line": 206, "column": 5}}, "type": "if", "locations": [{"start": {"line": 204, "column": 4}, "end": {"line": 206, "column": 5}}, {"start": {}, "end": {}}], "line": 204}, "4": {"loc": {"start": {"line": 248, "column": 4}, "end": {"line": 250, "column": 5}}, "type": "if", "locations": [{"start": {"line": 248, "column": 4}, "end": {"line": 250, "column": 5}}, {"start": {}, "end": {}}], "line": 248}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 2, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 2, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 2, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 2, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 2, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 2}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "340f6cb7929cfd070993965ed3f50a22269c5173"}, "C:\\my-projact\\WebCore\\server\\routes\\middleware\\roleCheck.js": {"path": "C:\\my-projact\\WebCore\\server\\routes\\middleware\\roleCheck.js", "statementMap": {"0": {"start": {"line": 1, "column": 26}, "end": {"line": 1, "column": 69}}, "1": {"start": {"line": 10, "column": 18}, "end": {"line": 10, "column": 20}}, "2": {"start": {"line": 13, "column": 2}, "end": {"line": 25, "column": 3}}, "3": {"start": {"line": 14, "column": 4}, "end": {"line": 14, "column": 25}}, "4": {"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 6}}, "5": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 56}}, "6": {"start": {"line": 18, "column": 9}, "end": {"line": 25, "column": 3}}, "7": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 70}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "9": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 74}}, "10": {"start": {"line": 23, "column": 54}, "end": {"line": 23, "column": 72}}, "11": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 38}}, "12": {"start": {"line": 37, "column": 2}, "end": {"line": 43, "column": 3}}, "13": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 7}}, "14": {"start": {"line": 42, "column": 4}, "end": {"line": 42, "column": 17}}, "15": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 14}}, "16": {"start": {"line": 51, "column": 18}, "end": {"line": 93, "column": 1}}, "17": {"start": {"line": 52, "column": 2}, "end": {"line": 92, "column": 4}}, "18": {"start": {"line": 53, "column": 4}, "end": {"line": 91, "column": 5}}, "19": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 46}}, "20": {"start": {"line": 54, "column": 39}, "end": {"line": 54, "column": 46}}, "21": {"start": {"line": 56, "column": 25}, "end": {"line": 56, "column": 87}}, "22": {"start": {"line": 57, "column": 43}, "end": {"line": 57, "column": 75}}, "23": {"start": {"line": 59, "column": 6}, "end": {"line": 64, "column": 7}}, "24": {"start": {"line": 60, "column": 8}, "end": {"line": 63, "column": 11}}, "25": {"start": {"line": 66, "column": 6}, "end": {"line": 71, "column": 7}}, "26": {"start": {"line": 67, "column": 8}, "end": {"line": 70, "column": 11}}, "27": {"start": {"line": 73, "column": 30}, "end": {"line": 73, "column": 79}}, "28": {"start": {"line": 73, "column": 54}, "end": {"line": 73, "column": 78}}, "29": {"start": {"line": 75, "column": 6}, "end": {"line": 80, "column": 7}}, "30": {"start": {"line": 76, "column": 8}, "end": {"line": 79, "column": 11}}, "31": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 32}}, "32": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 40}}, "33": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 13}}, "34": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 48}}, "35": {"start": {"line": 87, "column": 6}, "end": {"line": 90, "column": 9}}, "36": {"start": {"line": 101, "column": 2}, "end": {"line": 103, "column": 3}}, "37": {"start": {"line": 102, "column": 4}, "end": {"line": 102, "column": 20}}, "38": {"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": 3}}, "39": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": 29}}, "40": {"start": {"line": 109, "column": 2}, "end": {"line": 111, "column": 3}}, "41": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": 65}}, "42": {"start": {"line": 113, "column": 2}, "end": {"line": 113, "column": 14}}, "43": {"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 3}}, "44": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": 17}}, "45": {"start": {"line": 128, "column": 2}, "end": {"line": 137, "column": 3}}, "46": {"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 42}}, "47": {"start": {"line": 130, "column": 28}, "end": {"line": 130, "column": 44}}, "48": {"start": {"line": 132, "column": 4}, "end": {"line": 136, "column": 5}}, "49": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": 18}}, "50": {"start": {"line": 139, "column": 2}, "end": {"line": 139, "column": 15}}, "51": {"start": {"line": 147, "column": 24}, "end": {"line": 181, "column": 1}}, "52": {"start": {"line": 148, "column": 2}, "end": {"line": 180, "column": 4}}, "53": {"start": {"line": 149, "column": 4}, "end": {"line": 179, "column": 5}}, "54": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 46}}, "55": {"start": {"line": 150, "column": 39}, "end": {"line": 150, "column": 46}}, "56": {"start": {"line": 152, "column": 28}, "end": {"line": 152, "column": 68}}, "57": {"start": {"line": 154, "column": 6}, "end": {"line": 159, "column": 7}}, "58": {"start": {"line": 155, "column": 8}, "end": {"line": 158, "column": 11}}, "59": {"start": {"line": 161, "column": 6}, "end": {"line": 166, "column": 7}}, "60": {"start": {"line": 162, "column": 8}, "end": {"line": 165, "column": 11}}, "61": {"start": {"line": 168, "column": 6}, "end": {"line": 170, "column": 8}}, "62": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 40}}, "63": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 40}}, "64": {"start": {"line": 172, "column": 6}, "end": {"line": 172, "column": 13}}, "65": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 54}}, "66": {"start": {"line": 175, "column": 6}, "end": {"line": 178, "column": 9}}, "67": {"start": {"line": 186, "column": 21}, "end": {"line": 186, "column": 41}}, "68": {"start": {"line": 191, "column": 23}, "end": {"line": 191, "column": 57}}, "69": {"start": {"line": 193, "column": 0}, "end": {"line": 198, "column": 2}}}, "fnMap": {"0": {"name": "getUserRolesData", "decl": {"start": {"line": 8, "column": 15}, "end": {"line": 8, "column": 31}}, "loc": {"start": {"line": 8, "column": 38}, "end": {"line": 28, "column": 1}}, "line": 8}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 31}, "end": {"line": 15, "column": 32}}, "loc": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 56}}, "line": 16}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 23, "column": 42}, "end": {"line": 23, "column": 43}}, "loc": {"start": {"line": 23, "column": 54}, "end": {"line": 23, "column": 72}}, "line": 23}, "3": {"name": "validateUserAuth", "decl": {"start": {"line": 36, "column": 9}, "end": {"line": 36, "column": 25}}, "loc": {"start": {"line": 36, "column": 36}, "end": {"line": 45, "column": 1}}, "line": 36}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 51, "column": 18}, "end": {"line": 51, "column": 19}}, "loc": {"start": {"line": 51, "column": 37}, "end": {"line": 93, "column": 1}}, "line": 51}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 52, "column": 9}, "end": {"line": 52, "column": 10}}, "loc": {"start": {"line": 52, "column": 35}, "end": {"line": 92, "column": 3}}, "line": 52}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 73, "column": 46}, "end": {"line": 73, "column": 47}}, "loc": {"start": {"line": 73, "column": 54}, "end": {"line": 73, "column": 78}}, "line": 73}, "7": {"name": "getUserWithRolesForPermission", "decl": {"start": {"line": 100, "column": 15}, "end": {"line": 100, "column": 44}}, "loc": {"start": {"line": 100, "column": 50}, "end": {"line": 114, "column": 1}}, "line": 100}, "8": {"name": "hasUserPermission", "decl": {"start": {"line": 123, "column": 9}, "end": {"line": 123, "column": 26}}, "loc": {"start": {"line": 123, "column": 60}, "end": {"line": 140, "column": 1}}, "line": 123}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 147, "column": 24}, "end": {"line": 147, "column": 25}}, "loc": {"start": {"line": 147, "column": 46}, "end": {"line": 181, "column": 1}}, "line": 147}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 148, "column": 9}, "end": {"line": 148, "column": 10}}, "loc": {"start": {"line": 148, "column": 35}, "end": {"line": 180, "column": 3}}, "line": 148}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 168, "column": 46}, "end": {"line": 168, "column": 47}}, "loc": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 40}}, "line": 169}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 25, "column": 3}}, "type": "if", "locations": [{"start": {"line": 13, "column": 2}, "end": {"line": 25, "column": 3}}, {"start": {"line": 18, "column": 9}, "end": {"line": 25, "column": 3}}], "line": 13}, "1": {"loc": {"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 6}, "end": {"line": 13, "column": 16}}, {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": 45}}], "line": 13}, "2": {"loc": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 56}}, "type": "cond-expr", "locations": [{"start": {"line": 16, "column": 22}, "end": {"line": 16, "column": 40}}, {"start": {"line": 16, "column": 43}, "end": {"line": 16, "column": 56}}], "line": 16}, "3": {"loc": {"start": {"line": 18, "column": 9}, "end": {"line": 25, "column": 3}}, "type": "if", "locations": [{"start": {"line": 18, "column": 9}, "end": {"line": 25, "column": 3}}, {"start": {}, "end": {}}], "line": 18}, "4": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, {"start": {}, "end": {}}], "line": 22}, "5": {"loc": {"start": {"line": 37, "column": 2}, "end": {"line": 43, "column": 3}}, "type": "if", "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 43, "column": 3}}, {"start": {}, "end": {}}], "line": 37}, "6": {"loc": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 46}}, "type": "if", "locations": [{"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 46}}, {"start": {}, "end": {}}], "line": 54}, "7": {"loc": {"start": {"line": 56, "column": 25}, "end": {"line": 56, "column": 87}}, "type": "cond-expr", "locations": [{"start": {"line": 56, "column": 56}, "end": {"line": 56, "column": 69}}, {"start": {"line": 56, "column": 72}, "end": {"line": 56, "column": 87}}], "line": 56}, "8": {"loc": {"start": {"line": 59, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 59, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {}, "end": {}}], "line": 59}, "9": {"loc": {"start": {"line": 59, "column": 10}, "end": {"line": 59, "column": 39}}, "type": "binary-expr", "locations": [{"start": {"line": 59, "column": 10}, "end": {"line": 59, "column": 24}}, {"start": {"line": 59, "column": 28}, "end": {"line": 59, "column": 39}}], "line": 59}, "10": {"loc": {"start": {"line": 66, "column": 6}, "end": {"line": 71, "column": 7}}, "type": "if", "locations": [{"start": {"line": 66, "column": 6}, "end": {"line": 71, "column": 7}}, {"start": {}, "end": {}}], "line": 66}, "11": {"loc": {"start": {"line": 75, "column": 6}, "end": {"line": 80, "column": 7}}, "type": "if", "locations": [{"start": {"line": 75, "column": 6}, "end": {"line": 80, "column": 7}}, {"start": {}, "end": {}}], "line": 75}, "12": {"loc": {"start": {"line": 101, "column": 2}, "end": {"line": 103, "column": 3}}, "type": "if", "locations": [{"start": {"line": 101, "column": 2}, "end": {"line": 103, "column": 3}}, {"start": {}, "end": {}}], "line": 101}, "13": {"loc": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 20}}, {"start": {"line": 101, "column": 24}, "end": {"line": 101, "column": 53}}], "line": 101}, "14": {"loc": {"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": 3}}, "type": "if", "locations": [{"start": {"line": 105, "column": 2}, "end": {"line": 107, "column": 3}}, {"start": {}, "end": {}}], "line": 105}, "15": {"loc": {"start": {"line": 109, "column": 2}, "end": {"line": 111, "column": 3}}, "type": "if", "locations": [{"start": {"line": 109, "column": 2}, "end": {"line": 111, "column": 3}}, {"start": {}, "end": {}}], "line": 109}, "16": {"loc": {"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 3}}, "type": "if", "locations": [{"start": {"line": 124, "column": 2}, "end": {"line": 126, "column": 3}}, {"start": {}, "end": {}}], "line": 124}, "17": {"loc": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 26}}, {"start": {"line": 124, "column": 30}, "end": {"line": 124, "column": 65}}], "line": 124}, "18": {"loc": {"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 17}, "end": {"line": 129, "column": 30}}, {"start": {"line": 129, "column": 34}, "end": {"line": 129, "column": 42}}], "line": 129}, "19": {"loc": {"start": {"line": 132, "column": 4}, "end": {"line": 136, "column": 5}}, "type": "if", "locations": [{"start": {"line": 132, "column": 4}, "end": {"line": 136, "column": 5}}, {"start": {}, "end": {}}], "line": 132}, "20": {"loc": {"start": {"line": 132, "column": 8}, "end": {"line": 134, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": 23}}, {"start": {"line": 132, "column": 27}, "end": {"line": 132, "column": 52}}, {"start": {"line": 133, "column": 8}, "end": {"line": 133, "column": 48}}, {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 50}}], "line": 132}, "21": {"loc": {"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 46}}, "type": "if", "locations": [{"start": {"line": 150, "column": 6}, "end": {"line": 150, "column": 46}}, {"start": {}, "end": {}}], "line": 150}, "22": {"loc": {"start": {"line": 154, "column": 6}, "end": {"line": 159, "column": 7}}, "type": "if", "locations": [{"start": {"line": 154, "column": 6}, "end": {"line": 159, "column": 7}}, {"start": {}, "end": {}}], "line": 154}, "23": {"loc": {"start": {"line": 161, "column": 6}, "end": {"line": 166, "column": 7}}, "type": "if", "locations": [{"start": {"line": 161, "column": 6}, "end": {"line": 166, "column": 7}}, {"start": {}, "end": {}}], "line": 161}, "24": {"loc": {"start": {"line": 169, "column": 8}, "end": {"line": 169, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 169, "column": 18}, "end": {"line": 169, "column": 30}}, {"start": {"line": 169, "column": 33}, "end": {"line": 169, "column": 40}}], "line": 169}}, "s": {"0": 5, "1": 11, "2": 11, "3": 6, "4": 6, "5": 5, "6": 5, "7": 4, "8": 4, "9": 4, "10": 4, "11": 11, "12": 55, "13": 2, "14": 2, "15": 53, "16": 5, "17": 29, "18": 13, "19": 13, "20": 2, "21": 11, "22": 11, "23": 11, "24": 0, "25": 11, "26": 2, "27": 9, "28": 10, "29": 9, "30": 4, "31": 5, "32": 5, "33": 5, "34": 0, "35": 0, "36": 42, "37": 39, "38": 3, "39": 0, "40": 3, "41": 3, "42": 0, "43": 42, "44": 0, "45": 42, "46": 42, "47": 42, "48": 42, "49": 26, "50": 16, "51": 5, "52": 31, "53": 42, "54": 42, "55": 0, "56": 42, "57": 42, "58": 0, "59": 42, "60": 16, "61": 26, "62": 26, "63": 26, "64": 26, "65": 0, "66": 0, "67": 5, "68": 5, "69": 5}, "f": {"0": 11, "1": 5, "2": 4, "3": 55, "4": 29, "5": 13, "6": 10, "7": 42, "8": 42, "9": 31, "10": 42, "11": 26}, "b": {"0": [6, 5], "1": [11, 6], "2": [5, 0], "3": [4, 1], "4": [4, 0], "5": [2, 53], "6": [2, 11], "7": [11, 0], "8": [0, 11], "9": [11, 1], "10": [2, 9], "11": [4, 5], "12": [39, 3], "13": [42, 39], "14": [0, 3], "15": [3, 0], "16": [0, 42], "17": [42, 42], "18": [42, 0], "19": [26, 16], "20": [42, 41, 26, 26], "21": [0, 42], "22": [0, 42], "23": [16, 26], "24": [26, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "5843380890d5e48396acf6f2ac6db3d09e782e22"}, "C:\\my-projact\\WebCore\\server\\services\\ModuleService.js": {"path": "C:\\my-projact\\WebCore\\server\\services\\ModuleService.js", "statementMap": {"0": {"start": {"line": 1, "column": 25}, "end": {"line": 1, "column": 50}}, "1": {"start": {"line": 2, "column": 11}, "end": {"line": 2, "column": 33}}, "2": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 28}}, "3": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 33}}, "4": {"start": {"line": 6, "column": 15}, "end": {"line": 6, "column": 33}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 56, "column": 5}}, "6": {"start": {"line": 16, "column": 43}, "end": {"line": 16, "column": 50}}, "7": {"start": {"line": 18, "column": 26}, "end": {"line": 18, "column": 28}}, "8": {"start": {"line": 20, "column": 6}, "end": {"line": 22, "column": 7}}, "9": {"start": {"line": 21, "column": 8}, "end": {"line": 21, "column": 40}}, "10": {"start": {"line": 24, "column": 6}, "end": {"line": 26, "column": 7}}, "11": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": 36}}, "12": {"start": {"line": 28, "column": 6}, "end": {"line": 34, "column": 7}}, "13": {"start": {"line": 29, "column": 8}, "end": {"line": 33, "column": 10}}, "14": {"start": {"line": 36, "column": 22}, "end": {"line": 50, "column": 8}}, "15": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 21}}, "16": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 54}}, "17": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": 49}}, "18": {"start": {"line": 65, "column": 4}, "end": {"line": 86, "column": 5}}, "19": {"start": {"line": 66, "column": 21}, "end": {"line": 76, "column": 8}}, "20": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "21": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": 44}}, "22": {"start": {"line": 82, "column": 6}, "end": {"line": 82, "column": 20}}, "23": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 53}}, "24": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 18}}, "25": {"start": {"line": 96, "column": 4}, "end": {"line": 118, "column": 5}}, "26": {"start": {"line": 97, "column": 21}, "end": {"line": 112, "column": 8}}, "27": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 20}}, "28": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 53}}, "29": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 49}}, "30": {"start": {"line": 128, "column": 4}, "end": {"line": 149, "column": 5}}, "31": {"start": {"line": 129, "column": 21}, "end": {"line": 143, "column": 8}}, "32": {"start": {"line": 145, "column": 6}, "end": {"line": 145, "column": 20}}, "33": {"start": {"line": 147, "column": 6}, "end": {"line": 147, "column": 53}}, "34": {"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 49}}, "35": {"start": {"line": 159, "column": 4}, "end": {"line": 175, "column": 5}}, "36": {"start": {"line": 160, "column": 25}, "end": {"line": 164, "column": 7}}, "37": {"start": {"line": 166, "column": 21}, "end": {"line": 169, "column": 8}}, "38": {"start": {"line": 171, "column": 6}, "end": {"line": 171, "column": 20}}, "39": {"start": {"line": 173, "column": 6}, "end": {"line": 173, "column": 53}}, "40": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 56}}, "41": {"start": {"line": 184, "column": 4}, "end": {"line": 212, "column": 5}}, "42": {"start": {"line": 186, "column": 21}, "end": {"line": 188, "column": 8}}, "43": {"start": {"line": 190, "column": 6}, "end": {"line": 192, "column": 7}}, "44": {"start": {"line": 191, "column": 8}, "end": {"line": 191, "column": 44}}, "45": {"start": {"line": 195, "column": 6}, "end": {"line": 201, "column": 7}}, "46": {"start": {"line": 196, "column": 8}, "end": {"line": 200, "column": 9}}, "47": {"start": {"line": 197, "column": 10}, "end": {"line": 197, "column": 43}}, "48": {"start": {"line": 199, "column": 10}, "end": {"line": 199, "column": 75}}, "49": {"start": {"line": 204, "column": 6}, "end": {"line": 206, "column": 9}}, "50": {"start": {"line": 208, "column": 6}, "end": {"line": 208, "column": 18}}, "51": {"start": {"line": 210, "column": 6}, "end": {"line": 210, "column": 53}}, "52": {"start": {"line": 211, "column": 6}, "end": {"line": 211, "column": 49}}, "53": {"start": {"line": 222, "column": 4}, "end": {"line": 277, "column": 5}}, "54": {"start": {"line": 224, "column": 25}, "end": {"line": 224, "column": 67}}, "55": {"start": {"line": 225, "column": 6}, "end": {"line": 225, "column": 54}}, "56": {"start": {"line": 228, "column": 23}, "end": {"line": 228, "column": 59}}, "57": {"start": {"line": 229, "column": 23}, "end": {"line": 229, "column": 54}}, "58": {"start": {"line": 230, "column": 6}, "end": {"line": 230, "column": 48}}, "59": {"start": {"line": 233, "column": 18}, "end": {"line": 233, "column": 38}}, "60": {"start": {"line": 234, "column": 25}, "end": {"line": 234, "column": 41}}, "61": {"start": {"line": 237, "column": 26}, "end": {"line": 237, "column": 85}}, "62": {"start": {"line": 237, "column": 51}, "end": {"line": 237, "column": 84}}, "63": {"start": {"line": 238, "column": 6}, "end": {"line": 240, "column": 7}}, "64": {"start": {"line": 239, "column": 8}, "end": {"line": 239, "column": 65}}, "65": {"start": {"line": 242, "column": 27}, "end": {"line": 242, "column": 71}}, "66": {"start": {"line": 245, "column": 6}, "end": {"line": 247, "column": 7}}, "67": {"start": {"line": 246, "column": 8}, "end": {"line": 246, "column": 73}}, "68": {"start": {"line": 250, "column": 20}, "end": {"line": 250, "column": 43}}, "69": {"start": {"line": 251, "column": 23}, "end": {"line": 251, "column": 54}}, "70": {"start": {"line": 254, "column": 25}, "end": {"line": 269, "column": 7}}, "71": {"start": {"line": 271, "column": 21}, "end": {"line": 271, "column": 64}}, "72": {"start": {"line": 273, "column": 6}, "end": {"line": 273, "column": 20}}, "73": {"start": {"line": 275, "column": 6}, "end": {"line": 275, "column": 54}}, "74": {"start": {"line": 276, "column": 6}, "end": {"line": 276, "column": 18}}, "75": {"start": {"line": 286, "column": 4}, "end": {"line": 305, "column": 5}}, "76": {"start": {"line": 287, "column": 21}, "end": {"line": 287, "column": 55}}, "77": {"start": {"line": 290, "column": 22}, "end": {"line": 299, "column": 7}}, "78": {"start": {"line": 301, "column": 6}, "end": {"line": 301, "column": 21}}, "79": {"start": {"line": 303, "column": 6}, "end": {"line": 303, "column": 59}}, "80": {"start": {"line": 304, "column": 6}, "end": {"line": 304, "column": 18}}, "81": {"start": {"line": 313, "column": 4}, "end": {"line": 342, "column": 5}}, "82": {"start": {"line": 314, "column": 22}, "end": {"line": 330, "column": 8}}, "83": {"start": {"line": 332, "column": 6}, "end": {"line": 338, "column": 10}}, "84": {"start": {"line": 332, "column": 36}, "end": {"line": 338, "column": 7}}, "85": {"start": {"line": 340, "column": 6}, "end": {"line": 340, "column": 65}}, "86": {"start": {"line": 341, "column": 6}, "end": {"line": 341, "column": 60}}, "87": {"start": {"line": 351, "column": 4}, "end": {"line": 351, "column": 38}}, "88": {"start": {"line": 351, "column": 21}, "end": {"line": 351, "column": 38}}, "89": {"start": {"line": 353, "column": 14}, "end": {"line": 353, "column": 18}}, "90": {"start": {"line": 354, "column": 18}, "end": {"line": 354, "column": 45}}, "91": {"start": {"line": 355, "column": 14}, "end": {"line": 355, "column": 55}}, "92": {"start": {"line": 357, "column": 4}, "end": {"line": 357, "column": 76}}, "93": {"start": {"line": 365, "column": 4}, "end": {"line": 380, "column": 5}}, "94": {"start": {"line": 366, "column": 25}, "end": {"line": 371, "column": 8}}, "95": {"start": {"line": 373, "column": 6}, "end": {"line": 376, "column": 10}}, "96": {"start": {"line": 373, "column": 36}, "end": {"line": 376, "column": 7}}, "97": {"start": {"line": 378, "column": 6}, "end": {"line": 378, "column": 57}}, "98": {"start": {"line": 379, "column": 6}, "end": {"line": 379, "column": 52}}, "99": {"start": {"line": 384, "column": 0}, "end": {"line": 384, "column": 37}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 3}}, "loc": {"start": {"line": 14, "column": 36}, "end": {"line": 57, "column": 3}}, "line": 14}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 3}}, "loc": {"start": {"line": 64, "column": 32}, "end": {"line": 87, "column": 3}}, "line": 64}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 95, "column": 2}, "end": {"line": 95, "column": 3}}, "loc": {"start": {"line": 95, "column": 41}, "end": {"line": 119, "column": 3}}, "line": 95}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 127, "column": 2}, "end": {"line": 127, "column": 3}}, "loc": {"start": {"line": 127, "column": 43}, "end": {"line": 150, "column": 3}}, "line": 127}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 158, "column": 2}, "end": {"line": 158, "column": 3}}, "loc": {"start": {"line": 158, "column": 40}, "end": {"line": 176, "column": 3}}, "line": 158}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 183, "column": 2}, "end": {"line": 183, "column": 3}}, "loc": {"start": {"line": 183, "column": 31}, "end": {"line": 213, "column": 3}}, "line": 183}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 221, "column": 2}, "end": {"line": 221, "column": 3}}, "loc": {"start": {"line": 221, "column": 35}, "end": {"line": 278, "column": 3}}, "line": 221}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 237, "column": 42}, "end": {"line": 237, "column": 43}}, "loc": {"start": {"line": 237, "column": 51}, "end": {"line": 237, "column": 84}}, "line": 237}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 285, "column": 2}, "end": {"line": 285, "column": 3}}, "loc": {"start": {"line": 285, "column": 34}, "end": {"line": 306, "column": 3}}, "line": 285}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 312, "column": 2}, "end": {"line": 312, "column": 3}}, "loc": {"start": {"line": 312, "column": 31}, "end": {"line": 343, "column": 3}}, "line": 312}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 332, "column": 25}, "end": {"line": 332, "column": 26}}, "loc": {"start": {"line": 332, "column": 36}, "end": {"line": 338, "column": 7}}, "line": 332}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 350, "column": 2}, "end": {"line": 350, "column": 3}}, "loc": {"start": {"line": 350, "column": 24}, "end": {"line": 358, "column": 3}}, "line": 350}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 364, "column": 2}, "end": {"line": 364, "column": 3}}, "loc": {"start": {"line": 364, "column": 30}, "end": {"line": 381, "column": 3}}, "line": 364}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 373, "column": 28}, "end": {"line": 373, "column": 29}}, "loc": {"start": {"line": 373, "column": 36}, "end": {"line": 376, "column": 7}}, "line": 373}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 22}, "end": {"line": 14, "column": 34}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 32}, "end": {"line": 14, "column": 34}}], "line": 14}, "1": {"loc": {"start": {"line": 20, "column": 6}, "end": {"line": 22, "column": 7}}, "type": "if", "locations": [{"start": {"line": 20, "column": 6}, "end": {"line": 22, "column": 7}}, {"start": {}, "end": {}}], "line": 20}, "2": {"loc": {"start": {"line": 20, "column": 10}, "end": {"line": 20, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 10}, "end": {"line": 20, "column": 18}}, {"start": {"line": 20, "column": 22}, "end": {"line": 20, "column": 40}}], "line": 20}, "3": {"loc": {"start": {"line": 24, "column": 6}, "end": {"line": 26, "column": 7}}, "type": "if", "locations": [{"start": {"line": 24, "column": 6}, "end": {"line": 26, "column": 7}}, {"start": {}, "end": {}}], "line": 24}, "4": {"loc": {"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 10}, "end": {"line": 24, "column": 16}}, {"start": {"line": 24, "column": 20}, "end": {"line": 24, "column": 36}}], "line": 24}, "5": {"loc": {"start": {"line": 28, "column": 6}, "end": {"line": 34, "column": 7}}, "type": "if", "locations": [{"start": {"line": 28, "column": 6}, "end": {"line": 34, "column": 7}}, {"start": {}, "end": {}}], "line": 28}, "6": {"loc": {"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, "type": "if", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 80, "column": 7}}, {"start": {}, "end": {}}], "line": 78}, "7": {"loc": {"start": {"line": 162, "column": 16}, "end": {"line": 162, "column": 47}}, "type": "cond-expr", "locations": [{"start": {"line": 162, "column": 26}, "end": {"line": 162, "column": 34}}, {"start": {"line": 162, "column": 37}, "end": {"line": 162, "column": 47}}], "line": 162}, "8": {"loc": {"start": {"line": 190, "column": 6}, "end": {"line": 192, "column": 7}}, "type": "if", "locations": [{"start": {"line": 190, "column": 6}, "end": {"line": 192, "column": 7}}, {"start": {}, "end": {}}], "line": 190}, "9": {"loc": {"start": {"line": 195, "column": 6}, "end": {"line": 201, "column": 7}}, "type": "if", "locations": [{"start": {"line": 195, "column": 6}, "end": {"line": 201, "column": 7}}, {"start": {}, "end": {}}], "line": 195}, "10": {"loc": {"start": {"line": 238, "column": 6}, "end": {"line": 240, "column": 7}}, "type": "if", "locations": [{"start": {"line": 238, "column": 6}, "end": {"line": 240, "column": 7}}, {"start": {}, "end": {}}], "line": 238}, "11": {"loc": {"start": {"line": 245, "column": 6}, "end": {"line": 247, "column": 7}}, "type": "if", "locations": [{"start": {"line": 245, "column": 6}, "end": {"line": 247, "column": 7}}, {"start": {}, "end": {}}], "line": 245}, "12": {"loc": {"start": {"line": 245, "column": 10}, "end": {"line": 245, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 245, "column": 10}, "end": {"line": 245, "column": 28}}, {"start": {"line": 245, "column": 32}, "end": {"line": 245, "column": 53}}], "line": 245}, "13": {"loc": {"start": {"line": 256, "column": 21}, "end": {"line": 256, "column": 66}}, "type": "binary-expr", "locations": [{"start": {"line": 256, "column": 21}, "end": {"line": 256, "column": 45}}, {"start": {"line": 256, "column": 49}, "end": {"line": 256, "column": 66}}], "line": 256}, "14": {"loc": {"start": {"line": 258, "column": 21}, "end": {"line": 258, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 258, "column": 21}, "end": {"line": 258, "column": 45}}, {"start": {"line": 258, "column": 49}, "end": {"line": 258, "column": 51}}], "line": 258}, "15": {"loc": {"start": {"line": 259, "column": 16}, "end": {"line": 259, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 259, "column": 16}, "end": {"line": 259, "column": 35}}, {"start": {"line": 259, "column": 39}, "end": {"line": 259, "column": 48}}], "line": 259}, "16": {"loc": {"start": {"line": 260, "column": 18}, "end": {"line": 260, "column": 52}}, "type": "binary-expr", "locations": [{"start": {"line": 260, "column": 18}, "end": {"line": 260, "column": 39}}, {"start": {"line": 260, "column": 43}, "end": {"line": 260, "column": 52}}], "line": 260}, "17": {"loc": {"start": {"line": 261, "column": 14}, "end": {"line": 261, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 261, "column": 14}, "end": {"line": 261, "column": 31}}, {"start": {"line": 261, "column": 35}, "end": {"line": 261, "column": 44}}], "line": 261}, "18": {"loc": {"start": {"line": 262, "column": 15}, "end": {"line": 262, "column": 74}}, "type": "binary-expr", "locations": [{"start": {"line": 262, "column": 15}, "end": {"line": 262, "column": 33}}, {"start": {"line": 262, "column": 37}, "end": {"line": 262, "column": 74}}], "line": 262}, "19": {"loc": {"start": {"line": 263, "column": 22}, "end": {"line": 263, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 263, "column": 22}, "end": {"line": 263, "column": 47}}, {"start": {"line": 263, "column": 51}, "end": {"line": 263, "column": 53}}], "line": 263}, "20": {"loc": {"start": {"line": 264, "column": 16}, "end": {"line": 264, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 264, "column": 16}, "end": {"line": 264, "column": 35}}, {"start": {"line": 264, "column": 39}, "end": {"line": 264, "column": 41}}], "line": 264}, "21": {"loc": {"start": {"line": 297, "column": 16}, "end": {"line": 297, "column": 81}}, "type": "cond-expr", "locations": [{"start": {"line": 297, "column": 44}, "end": {"line": 297, "column": 76}}, {"start": {"line": 297, "column": 79}, "end": {"line": 297, "column": 81}}], "line": 297}, "22": {"loc": {"start": {"line": 351, "column": 4}, "end": {"line": 351, "column": 38}}, "type": "if", "locations": [{"start": {"line": 351, "column": 4}, "end": {"line": 351, "column": 38}}, {"start": {}, "end": {}}], "line": 351}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0]}}, "C:\\my-projact\\WebCore\\server\\services\\SettingsService.js": {"path": "C:\\my-projact\\WebCore\\server\\services\\SettingsService.js", "statementMap": {"0": {"start": {"line": 1, "column": 25}, "end": {"line": 1, "column": 50}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 33}}, "2": {"start": {"line": 10, "column": 4}, "end": {"line": 39, "column": 5}}, "3": {"start": {"line": 11, "column": 23}, "end": {"line": 16, "column": 8}}, "4": {"start": {"line": 19, "column": 30}, "end": {"line": 34, "column": 12}}, "5": {"start": {"line": 20, "column": 8}, "end": {"line": 22, "column": 9}}, "6": {"start": {"line": 21, "column": 10}, "end": {"line": 21, "column": 37}}, "7": {"start": {"line": 25, "column": 20}, "end": {"line": 25, "column": 33}}, "8": {"start": {"line": 26, "column": 8}, "end": {"line": 30, "column": 9}}, "9": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 44}}, "10": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 51}}, "11": {"start": {"line": 33, "column": 8}, "end": {"line": 33, "column": 19}}, "12": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 29}}, "13": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": 66}}, "14": {"start": {"line": 46, "column": 4}, "end": {"line": 66, "column": 5}}, "15": {"start": {"line": 47, "column": 23}, "end": {"line": 50, "column": 8}}, "16": {"start": {"line": 52, "column": 31}, "end": {"line": 52, "column": 33}}, "17": {"start": {"line": 53, "column": 6}, "end": {"line": 61, "column": 9}}, "18": {"start": {"line": 54, "column": 20}, "end": {"line": 54, "column": 33}}, "19": {"start": {"line": 55, "column": 8}, "end": {"line": 59, "column": 9}}, "20": {"start": {"line": 56, "column": 10}, "end": {"line": 56, "column": 44}}, "21": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": 46}}, "22": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 30}}, "23": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 91}}, "24": {"start": {"line": 73, "column": 4}, "end": {"line": 105, "column": 5}}, "25": {"start": {"line": 74, "column": 25}, "end": {"line": 74, "column": 27}}, "26": {"start": {"line": 76, "column": 6}, "end": {"line": 99, "column": 7}}, "27": {"start": {"line": 77, "column": 8}, "end": {"line": 98, "column": 9}}, "28": {"start": {"line": 78, "column": 30}, "end": {"line": 78, "column": 95}}, "29": {"start": {"line": 80, "column": 10}, "end": {"line": 97, "column": 12}}, "30": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 44}}, "31": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": 73}}, "32": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 69}}, "33": {"start": {"line": 112, "column": 4}, "end": {"line": 130, "column": 5}}, "34": {"start": {"line": 113, "column": 22}, "end": {"line": 117, "column": 8}}, "35": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, "36": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 20}}, "37": {"start": {"line": 123, "column": 6}, "end": {"line": 127, "column": 7}}, "38": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 41}}, "39": {"start": {"line": 126, "column": 8}, "end": {"line": 126, "column": 29}}, "40": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": 84}}, "41": {"start": {"line": 137, "column": 4}, "end": {"line": 160, "column": 5}}, "42": {"start": {"line": 138, "column": 26}, "end": {"line": 138, "column": 91}}, "43": {"start": {"line": 140, "column": 22}, "end": {"line": 155, "column": 8}}, "44": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 21}}, "45": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": 84}}, "46": {"start": {"line": 167, "column": 4}, "end": {"line": 218, "column": 5}}, "47": {"start": {"line": 168, "column": 30}, "end": {"line": 201, "column": 7}}, "48": {"start": {"line": 203, "column": 6}, "end": {"line": 213, "column": 7}}, "49": {"start": {"line": 204, "column": 8}, "end": {"line": 212, "column": 9}}, "50": {"start": {"line": 205, "column": 34}, "end": {"line": 207, "column": 12}}, "51": {"start": {"line": 209, "column": 10}, "end": {"line": 211, "column": 11}}, "52": {"start": {"line": 210, "column": 12}, "end": {"line": 210, "column": 56}}, "53": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 72}}, "54": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 81}}, "55": {"start": {"line": 225, "column": 27}, "end": {"line": 232, "column": 5}}, "56": {"start": {"line": 234, "column": 4}, "end": {"line": 234, "column": 57}}, "57": {"start": {"line": 238, "column": 0}, "end": {"line": 238, "column": 39}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 3}}, "loc": {"start": {"line": 9, "column": 25}, "end": {"line": 40, "column": 3}}, "line": 9}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 19, "column": 46}, "end": {"line": 19, "column": 47}}, "loc": {"start": {"line": 19, "column": 64}, "end": {"line": 34, "column": 7}}, "line": 19}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 3}}, "loc": {"start": {"line": 45, "column": 40}, "end": {"line": 67, "column": 3}}, "line": 45}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 53, "column": 23}, "end": {"line": 53, "column": 24}}, "loc": {"start": {"line": 53, "column": 34}, "end": {"line": 61, "column": 7}}, "line": 53}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 3}}, "loc": {"start": {"line": 72, "column": 37}, "end": {"line": 106, "column": 3}}, "line": 72}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 3}}, "loc": {"start": {"line": 111, "column": 34}, "end": {"line": 131, "column": 3}}, "line": 111}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 136, "column": 2}, "end": {"line": 136, "column": 3}}, "loc": {"start": {"line": 136, "column": 61}, "end": {"line": 161, "column": 3}}, "line": 136}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 166, "column": 2}, "end": {"line": 166, "column": 3}}, "loc": {"start": {"line": 166, "column": 36}, "end": {"line": 219, "column": 3}}, "line": 166}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 224, "column": 2}, "end": {"line": 224, "column": 3}}, "loc": {"start": {"line": 224, "column": 33}, "end": {"line": 235, "column": 3}}, "line": 224}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 8}, "end": {"line": 22, "column": 9}}, "type": "if", "locations": [{"start": {"line": 20, "column": 8}, "end": {"line": 22, "column": 9}}, {"start": {}, "end": {}}], "line": 20}, "1": {"loc": {"start": {"line": 78, "column": 30}, "end": {"line": 78, "column": 95}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 58}, "end": {"line": 78, "column": 79}}, {"start": {"line": 78, "column": 82}, "end": {"line": 78, "column": 95}}], "line": 78}, "2": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, {"start": {}, "end": {}}], "line": 119}, "3": {"loc": {"start": {"line": 136, "column": 41}, "end": {"line": 136, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 136, "column": 55}, "end": {"line": 136, "column": 59}}], "line": 136}, "4": {"loc": {"start": {"line": 138, "column": 26}, "end": {"line": 138, "column": 91}}, "type": "cond-expr", "locations": [{"start": {"line": 138, "column": 54}, "end": {"line": 138, "column": 75}}, {"start": {"line": 138, "column": 78}, "end": {"line": 138, "column": 91}}], "line": 138}, "5": {"loc": {"start": {"line": 152, "column": 23}, "end": {"line": 152, "column": 67}}, "type": "binary-expr", "locations": [{"start": {"line": 152, "column": 23}, "end": {"line": 152, "column": 34}}, {"start": {"line": 152, "column": 38}, "end": {"line": 152, "column": 67}}], "line": 152}, "6": {"loc": {"start": {"line": 209, "column": 10}, "end": {"line": 211, "column": 11}}, "type": "if", "locations": [{"start": {"line": 209, "column": 10}, "end": {"line": 211, "column": 11}}, {"start": {}, "end": {}}], "line": 209}}, "s": {"0": 2, "1": 2, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 2}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a26efb5e961be8e81a46e2b3c153bd71d1edadf9"}, "C:\\my-projact\\WebCore\\server\\services\\llmService.js": {"path": "C:\\my-projact\\WebCore\\server\\services\\llmService.js", "statementMap": {"0": {"start": {"line": 1, "column": 14}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 15}, "end": {"line": 2, "column": 32}}, "2": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": 46}}, "3": {"start": {"line": 4, "column": 15}, "end": {"line": 4, "column": 32}}, "4": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 16}}, "5": {"start": {"line": 8, "column": 15}, "end": {"line": 10, "column": 2}}, "6": {"start": {"line": 12, "column": 18}, "end": {"line": 14, "column": 2}}, "7": {"start": {"line": 16, "column": 20}, "end": {"line": 16, "column": 21}}, "8": {"start": {"line": 17, "column": 20}, "end": {"line": 17, "column": 24}}, "9": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 57}}, "10": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 55}}, "11": {"start": {"line": 24, "column": 2}, "end": {"line": 37, "column": 3}}, "12": {"start": {"line": 24, "column": 15}, "end": {"line": 24, "column": 16}}, "13": {"start": {"line": 25, "column": 4}, "end": {"line": 36, "column": 5}}, "14": {"start": {"line": 26, "column": 23}, "end": {"line": 30, "column": 8}}, "15": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 49}}, "16": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 103}}, "17": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 45}}, "18": {"start": {"line": 34, "column": 33}, "end": {"line": 34, "column": 45}}, "19": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 31}}, "20": {"start": {"line": 41, "column": 2}, "end": {"line": 56, "column": 3}}, "21": {"start": {"line": 41, "column": 15}, "end": {"line": 41, "column": 16}}, "22": {"start": {"line": 42, "column": 4}, "end": {"line": 55, "column": 5}}, "23": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": 95}}, "24": {"start": {"line": 44, "column": 23}, "end": {"line": 48, "column": 8}}, "25": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": 91}}, "26": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": 38}}, "27": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": 106}}, "28": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 45}}, "29": {"start": {"line": 53, "column": 33}, "end": {"line": 53, "column": 45}}, "30": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 31}}, "31": {"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 3}}, "32": {"start": {"line": 62, "column": 6}, "end": {"line": 62, "column": 49}}, "33": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": 52}}, "34": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": 63}}, "35": {"start": {"line": 70, "column": 0}, "end": {"line": 72, "column": 2}}}, "fnMap": {"0": {"name": "sleep", "decl": {"start": {"line": 19, "column": 15}, "end": {"line": 19, "column": 20}}, "loc": {"start": {"line": 19, "column": 25}, "end": {"line": 21, "column": 1}}, "line": 19}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 22}}, "loc": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 55}}, "line": 20}, "2": {"name": "sendRequestToOpenAI", "decl": {"start": {"line": 23, "column": 15}, "end": {"line": 23, "column": 34}}, "loc": {"start": {"line": 23, "column": 51}, "end": {"line": 38, "column": 1}}, "line": 23}, "3": {"name": "sendRequestToAnthropic", "decl": {"start": {"line": 40, "column": 15}, "end": {"line": 40, "column": 37}}, "loc": {"start": {"line": 40, "column": 54}, "end": {"line": 57, "column": 1}}, "line": 40}, "4": {"name": "sendLLMRequest", "decl": {"start": {"line": 59, "column": 15}, "end": {"line": 59, "column": 29}}, "loc": {"start": {"line": 59, "column": 56}, "end": {"line": 68, "column": 1}}, "line": 59}}, "branchMap": {"0": {"loc": {"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 45}}, "type": "if", "locations": [{"start": {"line": 34, "column": 6}, "end": {"line": 34, "column": 45}}, {"start": {}, "end": {}}], "line": 34}, "1": {"loc": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 45}}, "type": "if", "locations": [{"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 45}}, {"start": {}, "end": {}}], "line": 53}, "2": {"loc": {"start": {"line": 60, "column": 2}, "end": {"line": 67, "column": 3}}, "type": "switch", "locations": [{"start": {"line": 61, "column": 4}, "end": {"line": 62, "column": 49}}, {"start": {"line": 63, "column": 4}, "end": {"line": 64, "column": 52}}, {"start": {"line": 65, "column": 4}, "end": {"line": 66, "column": 63}}], "line": 60}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0, 0]}}, "C:\\my-projact\\WebCore\\server\\services\\prismaUserService.js": {"path": "C:\\my-projact\\WebCore\\server\\services\\prismaUserService.js", "statementMap": {"0": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 19}, "end": {"line": 2, "column": 49}}, "2": {"start": {"line": 3, "column": 51}, "end": {"line": 3, "column": 82}}, "3": {"start": {"line": 7, "column": 4}, "end": {"line": 24, "column": 5}}, "4": {"start": {"line": 8, "column": 6}, "end": {"line": 21, "column": 9}}, "5": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 76}}, "6": {"start": {"line": 28, "column": 4}, "end": {"line": 47, "column": 5}}, "7": {"start": {"line": 29, "column": 6}, "end": {"line": 44, "column": 9}}, "8": {"start": {"line": 46, "column": 6}, "end": {"line": 46, "column": 81}}, "9": {"start": {"line": 51, "column": 4}, "end": {"line": 64, "column": 5}}, "10": {"start": {"line": 52, "column": 6}, "end": {"line": 61, "column": 9}}, "11": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 84}}, "12": {"start": {"line": 68, "column": 4}, "end": {"line": 86, "column": 5}}, "13": {"start": {"line": 69, "column": 25}, "end": {"line": 69, "column": 36}}, "14": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 27}}, "15": {"start": {"line": 72, "column": 6}, "end": {"line": 83, "column": 9}}, "16": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 82}}, "17": {"start": {"line": 90, "column": 4}, "end": {"line": 100, "column": 5}}, "18": {"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": 9}}, "19": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 18}}, "20": {"start": {"line": 96, "column": 6}, "end": {"line": 98, "column": 7}}, "21": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 21}}, "22": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 82}}, "23": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 53}}, "24": {"start": {"line": 104, "column": 16}, "end": {"line": 104, "column": 53}}, "25": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 59}}, "26": {"start": {"line": 105, "column": 19}, "end": {"line": 105, "column": 59}}, "27": {"start": {"line": 107, "column": 4}, "end": {"line": 146, "column": 5}}, "28": {"start": {"line": 108, "column": 19}, "end": {"line": 117, "column": 8}}, "29": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 29}}, "30": {"start": {"line": 119, "column": 17}, "end": {"line": 119, "column": 29}}, "31": {"start": {"line": 121, "column": 28}, "end": {"line": 121, "column": 75}}, "32": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 38}}, "33": {"start": {"line": 122, "column": 26}, "end": {"line": 122, "column": 38}}, "34": {"start": {"line": 125, "column": 26}, "end": {"line": 141, "column": 8}}, "35": {"start": {"line": 143, "column": 6}, "end": {"line": 143, "column": 25}}, "36": {"start": {"line": 145, "column": 6}, "end": {"line": 145, "column": 91}}, "37": {"start": {"line": 150, "column": 4}, "end": {"line": 150, "column": 53}}, "38": {"start": {"line": 150, "column": 16}, "end": {"line": 150, "column": 53}}, "39": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 59}}, "40": {"start": {"line": 151, "column": 19}, "end": {"line": 151, "column": 59}}, "41": {"start": {"line": 153, "column": 25}, "end": {"line": 153, "column": 53}}, "42": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 77}}, "43": {"start": {"line": 154, "column": 22}, "end": {"line": 154, "column": 77}}, "44": {"start": {"line": 156, "column": 17}, "end": {"line": 156, "column": 53}}, "45": {"start": {"line": 158, "column": 4}, "end": {"line": 178, "column": 5}}, "46": {"start": {"line": 159, "column": 19}, "end": {"line": 173, "column": 8}}, "47": {"start": {"line": 175, "column": 6}, "end": {"line": 175, "column": 18}}, "48": {"start": {"line": 177, "column": 6}, "end": {"line": 177, "column": 80}}, "49": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 59}}, "50": {"start": {"line": 182, "column": 19}, "end": {"line": 182, "column": 59}}, "51": {"start": {"line": 184, "column": 17}, "end": {"line": 184, "column": 53}}, "52": {"start": {"line": 186, "column": 4}, "end": {"line": 200, "column": 5}}, "53": {"start": {"line": 187, "column": 6}, "end": {"line": 197, "column": 9}}, "54": {"start": {"line": 199, "column": 6}, "end": {"line": 199, "column": 98}}, "55": {"start": {"line": 204, "column": 4}, "end": {"line": 216, "column": 5}}, "56": {"start": {"line": 205, "column": 6}, "end": {"line": 213, "column": 9}}, "57": {"start": {"line": 215, "column": 6}, "end": {"line": 215, "column": 85}}, "58": {"start": {"line": 220, "column": 4}, "end": {"line": 232, "column": 5}}, "59": {"start": {"line": 221, "column": 6}, "end": {"line": 229, "column": 9}}, "60": {"start": {"line": 231, "column": 6}, "end": {"line": 231, "column": 92}}, "61": {"start": {"line": 236, "column": 4}, "end": {"line": 249, "column": 5}}, "62": {"start": {"line": 237, "column": 6}, "end": {"line": 246, "column": 9}}, "63": {"start": {"line": 248, "column": 6}, "end": {"line": 248, "column": 86}}, "64": {"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 27}}, "65": {"start": {"line": 254, "column": 15}, "end": {"line": 254, "column": 27}}, "66": {"start": {"line": 255, "column": 43}, "end": {"line": 255, "column": 47}}, "67": {"start": {"line": 256, "column": 4}, "end": {"line": 256, "column": 25}}, "68": {"start": {"line": 260, "column": 0}, "end": {"line": 260, "column": 35}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 3}}, "loc": {"start": {"line": 6, "column": 22}, "end": {"line": 25, "column": 3}}, "line": 6}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 3}}, "loc": {"start": {"line": 27, "column": 23}, "end": {"line": 48, "column": 3}}, "line": 27}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 3}}, "loc": {"start": {"line": 50, "column": 33}, "end": {"line": 65, "column": 3}}, "line": 50}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 3}}, "loc": {"start": {"line": 67, "column": 32}, "end": {"line": 87, "column": 3}}, "line": 67}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 3}}, "loc": {"start": {"line": 89, "column": 26}, "end": {"line": 101, "column": 3}}, "line": 89}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 103, "column": 2}, "end": {"line": 103, "column": 3}}, "loc": {"start": {"line": 103, "column": 57}, "end": {"line": 147, "column": 3}}, "line": 103}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 149, "column": 2}, "end": {"line": 149, "column": 3}}, "loc": {"start": {"line": 149, "column": 54}, "end": {"line": 179, "column": 3}}, "line": 149}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 181, "column": 2}, "end": {"line": 181, "column": 3}}, "loc": {"start": {"line": 181, "column": 45}, "end": {"line": 201, "column": 3}}, "line": 181}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 203, "column": 2}, "end": {"line": 203, "column": 3}}, "loc": {"start": {"line": 203, "column": 56}, "end": {"line": 217, "column": 3}}, "line": 203}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 219, "column": 2}, "end": {"line": 219, "column": 3}}, "loc": {"start": {"line": 219, "column": 48}, "end": {"line": 233, "column": 3}}, "line": 219}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 235, "column": 2}, "end": {"line": 235, "column": 3}}, "loc": {"start": {"line": 235, "column": 40}, "end": {"line": 250, "column": 3}}, "line": 235}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 253, "column": 2}, "end": {"line": 253, "column": 3}}, "loc": {"start": {"line": 253, "column": 28}, "end": {"line": 257, "column": 3}}, "line": 253}}, "branchMap": {"0": {"loc": {"start": {"line": 96, "column": 6}, "end": {"line": 98, "column": 7}}, "type": "if", "locations": [{"start": {"line": 96, "column": 6}, "end": {"line": 98, "column": 7}}, {"start": {}, "end": {}}], "line": 96}, "1": {"loc": {"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 53}}, "type": "if", "locations": [{"start": {"line": 104, "column": 4}, "end": {"line": 104, "column": 53}}, {"start": {}, "end": {}}], "line": 104}, "2": {"loc": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 59}}, "type": "if", "locations": [{"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": 59}}, {"start": {}, "end": {}}], "line": 105}, "3": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 29}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 119, "column": 29}}, {"start": {}, "end": {}}], "line": 119}, "4": {"loc": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 38}}, "type": "if", "locations": [{"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 38}}, {"start": {}, "end": {}}], "line": 122}, "5": {"loc": {"start": {"line": 149, "column": 41}, "end": {"line": 149, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 149, "column": 48}, "end": {"line": 149, "column": 50}}], "line": 149}, "6": {"loc": {"start": {"line": 150, "column": 4}, "end": {"line": 150, "column": 53}}, "type": "if", "locations": [{"start": {"line": 150, "column": 4}, "end": {"line": 150, "column": 53}}, {"start": {}, "end": {}}], "line": 150}, "7": {"loc": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 59}}, "type": "if", "locations": [{"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 59}}, {"start": {}, "end": {}}], "line": 151}, "8": {"loc": {"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 77}}, "type": "if", "locations": [{"start": {"line": 154, "column": 4}, "end": {"line": 154, "column": 77}}, {"start": {}, "end": {}}], "line": 154}, "9": {"loc": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 59}}, "type": "if", "locations": [{"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 59}}, {"start": {}, "end": {}}], "line": 182}, "10": {"loc": {"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 27}}, "type": "if", "locations": [{"start": {"line": 254, "column": 4}, "end": {"line": 254, "column": 27}}, {"start": {}, "end": {}}], "line": 254}}, "s": {"0": 5, "1": 5, "2": 5, "3": 0, "4": 0, "5": 0, "6": 32, "7": 32, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 48, "24": 0, "25": 48, "26": 0, "27": 48, "28": 48, "29": 48, "30": 0, "31": 48, "32": 48, "33": 0, "34": 48, "35": 48, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 48, "56": 48, "57": 0, "58": 0, "59": 0, "60": 0, "61": 7, "62": 7, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 5}, "f": {"0": 0, "1": 32, "2": 0, "3": 0, "4": 0, "5": 48, "6": 0, "7": 0, "8": 48, "9": 0, "10": 7, "11": 0}, "b": {"0": [0, 0], "1": [0, 48], "2": [0, 48], "3": [0, 48], "4": [0, 48], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "8b538c5fec46caedfc9c7a53a574cf8d1463ee1b"}, "C:\\my-projact\\WebCore\\server\\services\\userService.js": {"path": "C:\\my-projact\\WebCore\\server\\services\\userService.js", "statementMap": {"0": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 3, "column": 13}, "end": {"line": 3, "column": 41}}, "2": {"start": {"line": 4, "column": 51}, "end": {"line": 4, "column": 82}}, "3": {"start": {"line": 8, "column": 4}, "end": {"line": 12, "column": 5}}, "4": {"start": {"line": 9, "column": 6}, "end": {"line": 9, "column": 25}}, "5": {"start": {"line": 11, "column": 6}, "end": {"line": 11, "column": 68}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 20, "column": 5}}, "7": {"start": {"line": 17, "column": 6}, "end": {"line": 17, "column": 46}}, "8": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": 83}}, "9": {"start": {"line": 24, "column": 4}, "end": {"line": 28, "column": 5}}, "10": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 44}}, "11": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 86}}, "12": {"start": {"line": 32, "column": 4}, "end": {"line": 36, "column": 5}}, "13": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 84}}, "14": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 74}}, "15": {"start": {"line": 40, "column": 4}, "end": {"line": 45, "column": 5}}, "16": {"start": {"line": 41, "column": 21}, "end": {"line": 41, "column": 61}}, "17": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": 41}}, "18": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 74}}, "19": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 53}}, "20": {"start": {"line": 49, "column": 16}, "end": {"line": 49, "column": 53}}, "21": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 59}}, "22": {"start": {"line": 50, "column": 19}, "end": {"line": 50, "column": 59}}, "23": {"start": {"line": 52, "column": 4}, "end": {"line": 64, "column": 5}}, "24": {"start": {"line": 53, "column": 19}, "end": {"line": 53, "column": 53}}, "25": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 29}}, "26": {"start": {"line": 54, "column": 17}, "end": {"line": 54, "column": 29}}, "27": {"start": {"line": 56, "column": 28}, "end": {"line": 56, "column": 75}}, "28": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 38}}, "29": {"start": {"line": 57, "column": 26}, "end": {"line": 57, "column": 38}}, "30": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 36}}, "31": {"start": {"line": 60, "column": 26}, "end": {"line": 60, "column": 43}}, "32": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": 25}}, "33": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 97}}, "34": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 53}}, "35": {"start": {"line": 68, "column": 16}, "end": {"line": 68, "column": 53}}, "36": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 59}}, "37": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 59}}, "38": {"start": {"line": 71, "column": 25}, "end": {"line": 71, "column": 60}}, "39": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 77}}, "40": {"start": {"line": 72, "column": 22}, "end": {"line": 72, "column": 77}}, "41": {"start": {"line": 74, "column": 17}, "end": {"line": 74, "column": 53}}, "42": {"start": {"line": 76, "column": 4}, "end": {"line": 87, "column": 5}}, "43": {"start": {"line": 77, "column": 19}, "end": {"line": 81, "column": 8}}, "44": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": 24}}, "45": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": 18}}, "46": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 72}}, "47": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 59}}, "48": {"start": {"line": 91, "column": 19}, "end": {"line": 91, "column": 59}}, "49": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 57}}, "50": {"start": {"line": 94, "column": 4}, "end": {"line": 102, "column": 5}}, "51": {"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": 7}}, "52": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": 26}}, "53": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": 18}}, "54": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 76}}, "55": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 3}}, "loc": {"start": {"line": 7, "column": 22}, "end": {"line": 13, "column": 3}}, "line": 7}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 3}}, "loc": {"start": {"line": 15, "column": 23}, "end": {"line": 21, "column": 3}}, "line": 15}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 3}}, "loc": {"start": {"line": 23, "column": 33}, "end": {"line": 29, "column": 3}}, "line": 23}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 3}}, "loc": {"start": {"line": 31, "column": 32}, "end": {"line": 37, "column": 3}}, "line": 31}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 3}}, "loc": {"start": {"line": 39, "column": 26}, "end": {"line": 46, "column": 3}}, "line": 39}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 3}}, "loc": {"start": {"line": 48, "column": 57}, "end": {"line": 65, "column": 3}}, "line": 48}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 3}}, "loc": {"start": {"line": 67, "column": 54}, "end": {"line": 88, "column": 3}}, "line": 67}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 90, "column": 2}, "end": {"line": 90, "column": 3}}, "loc": {"start": {"line": 90, "column": 43}, "end": {"line": 103, "column": 3}}, "line": 90}}, "branchMap": {"0": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 53}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": 53}}, {"start": {}, "end": {}}], "line": 49}, "1": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 59}}, "type": "if", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 59}}, {"start": {}, "end": {}}], "line": 50}, "2": {"loc": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 29}}, "type": "if", "locations": [{"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 29}}, {"start": {}, "end": {}}], "line": 54}, "3": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 38}}, "type": "if", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 38}}, {"start": {}, "end": {}}], "line": 57}, "4": {"loc": {"start": {"line": 67, "column": 41}, "end": {"line": 67, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 67, "column": 48}, "end": {"line": 67, "column": 50}}], "line": 67}, "5": {"loc": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 53}}, "type": "if", "locations": [{"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 53}}, {"start": {}, "end": {}}], "line": 68}, "6": {"loc": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 59}}, "type": "if", "locations": [{"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 59}}, {"start": {}, "end": {}}], "line": 69}, "7": {"loc": {"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 77}}, "type": "if", "locations": [{"start": {"line": 72, "column": 4}, "end": {"line": 72, "column": 77}}, {"start": {}, "end": {}}], "line": 72}, "8": {"loc": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 59}}, "type": "if", "locations": [{"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 59}}, {"start": {}, "end": {}}], "line": 91}, "9": {"loc": {"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": 7}}, "type": "if", "locations": [{"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": 7}}, {"start": {}, "end": {}}], "line": 95}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0]}}}