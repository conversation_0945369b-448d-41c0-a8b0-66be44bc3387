const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class SettingsService {
  /**
   * Get all system settings grouped by category
   */
  async getAllSettings() {
    try {
      const settings = await prisma.systemSetting.findMany({
        orderBy: [
          { category: 'asc' },
          { key: 'asc' }
        ]
      });

      // Group settings by category
      const groupedSettings = settings.reduce((acc, setting) => {
        if (!acc[setting.category]) {
          acc[setting.category] = {};
        }
        
        // Parse JSON values if they exist
        let value = setting.value;
        try {
          value = JSON.parse(setting.value);
        } catch (e) {
          // Keep as string if not valid JSON
        }
        
        acc[setting.category][setting.key] = value;
        return acc;
      }, {});

      return groupedSettings;
    } catch (error) {
      throw new Error(`Failed to get settings: ${error.message}`);
    }
  }

  /**
   * Get settings by category
   */
  async getSettingsByCategory(category) {
    try {
      const settings = await prisma.systemSetting.findMany({
        where: { category },
        orderBy: { key: 'asc' }
      });

      const categorySettings = {};
      settings.forEach(setting => {
        let value = setting.value;
        try {
          value = JSON.parse(setting.value);
        } catch (e) {
          // Keep as string if not valid JSON
        }
        categorySettings[setting.key] = value;
      });

      return categorySettings;
    } catch (error) {
      throw new Error(`Failed to get settings for category ${category}: ${error.message}`);
    }
  }

  /**
   * Update multiple settings
   */
  async updateSettings(settingsData) {
    try {
      const operations = [];

      for (const [category, categorySettings] of Object.entries(settingsData)) {
        for (const [key, value] of Object.entries(categorySettings)) {
          const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
          
          operations.push(
            prisma.systemSetting.upsert({
              where: {
                key: `${category}.${key}`
              },
              update: {
                value: stringValue,
                updatedAt: new Date()
              },
              create: {
                key: `${category}.${key}`,
                value: stringValue,
                category: category,
                description: `${category} setting: ${key}`,
                isPublic: this.isPublicSetting(category, key)
              }
            })
          );
        }
      }

      await prisma.$transaction(operations);
      return { success: true, message: 'Settings updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update settings: ${error.message}`);
    }
  }

  /**
   * Get a specific setting value
   */
  async getSetting(category, key) {
    try {
      const setting = await prisma.systemSetting.findUnique({
        where: {
          key: `${category}.${key}`
        }
      });

      if (!setting) {
        return null;
      }

      try {
        return JSON.parse(setting.value);
      } catch (e) {
        return setting.value;
      }
    } catch (error) {
      throw new Error(`Failed to get setting ${category}.${key}: ${error.message}`);
    }
  }

  /**
   * Set a specific setting value
   */
  async setSetting(category, key, value, description = null) {
    try {
      const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
      
      const setting = await prisma.systemSetting.upsert({
        where: {
          key: `${category}.${key}`
        },
        update: {
          value: stringValue,
          updatedAt: new Date()
        },
        create: {
          key: `${category}.${key}`,
          value: stringValue,
          category: category,
          description: description || `${category} setting: ${key}`,
          isPublic: this.isPublicSetting(category, key)
        }
      });

      return setting;
    } catch (error) {
      throw new Error(`Failed to set setting ${category}.${key}: ${error.message}`);
    }
  }

  /**
   * Initialize default settings if they don't exist
   */
  async initializeDefaultSettings() {
    try {
      const defaultSettings = {
        general: {
          siteName: 'WebCore System',
          siteDescription: 'Advanced Web Management System',
          logo: '',
          timezone: 'UTC',
          language: 'en',
          maintenanceMode: false
        },
        company: {
          name: {
            en: 'WebCore Company',
            ar: 'شركة ويب كور'
          },
          description: {
            en: 'Advanced Web Management Solutions',
            ar: 'حلول إدارة الويب المتقدمة'
          },
          address: {
            en: '',
            ar: ''
          },
          phone: '',
          website: '',
          supportEmail: '',
          businessHours: {
            en: 'Monday - Friday: 9:00 AM - 6:00 PM',
            ar: 'الاثنين - الجمعة: 9:00 ص - 6:00 م'
          },
          socialMedia: {
            facebook: '',
            twitter: '',
            linkedin: '',
            instagram: ''
          },
          legalInfo: {
            registrationNumber: '',
            taxId: '',
            license: ''
          }
        },
        branding: {
          primaryLogo: '',
          secondaryLogo: '',
          favicon: '',
          primaryColor: '#3b82f6',
          secondaryColor: '#64748b',
          accentColor: '#10b981',
          backgroundColor: '#ffffff',
          textColor: '#1e293b'
        },
        security: {
          passwordMinLength: 8,
          passwordRequireSpecial: true,
          sessionTimeout: 30,
          maxLoginAttempts: 5,
          twoFactorRequired: false,
          ipWhitelist: []
        },
        notifications: {
          emailEnabled: true,
          smsEnabled: false,
          pushEnabled: true,
          adminEmail: '<EMAIL>',
          smtpHost: '',
          smtpPort: 587,
          smtpUser: '',
          smtpPassword: ''
        },
        emailTemplates: {
          welcome: {
            enabled: true,
            subject: {
              en: 'Welcome to {{companyName}}!',
              ar: 'مرحباً بك في {{companyName}}!'
            },
            body: {
              en: `Dear {{name}},

Welcome to {{companyName}}! We're excited to have you on board.

Your account has been successfully created and you can now access all our services.

If you have any questions, please don't hesitate to contact our support team.

Best regards,
{{companyName}} Team`,
              ar: `عزيزي {{name}}،

مرحباً بك في {{companyName}}! نحن متحمسون لانضمامك إلينا.

تم إنشاء حسابك بنجاح ويمكنك الآن الوصول إلى جميع خدماتنا.

إذا كان لديك أي أسئلة، لا تتردد في الاتصال بفريق الدعم لدينا.

مع أطيب التحيات،
فريق {{companyName}}`
            }
          },
          passwordReset: {
            enabled: true,
            subject: {
              en: 'Password Reset Request - {{companyName}}',
              ar: 'طلب إعادة تعيين كلمة المرور - {{companyName}}'
            },
            body: {
              en: `Dear {{name}},

We received a request to reset your password for your {{companyName}} account.

Click the link below to reset your password:
{{resetLink}}

If you didn't request this password reset, please ignore this email.

This link will expire in 24 hours for security reasons.

Best regards,
{{companyName}} Team`,
              ar: `عزيزي {{name}}،

تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك في {{companyName}}.

انقر على الرابط أدناه لإعادة تعيين كلمة المرور:
{{resetLink}}

إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد الإلكتروني.

سينتهي صلاحية هذا الرابط خلال 24 ساعة لأسباب أمنية.

مع أطيب التحيات،
فريق {{companyName}}`
            }
          },
          accountSuspended: {
            enabled: true,
            subject: {
              en: 'Account Suspended - {{companyName}}',
              ar: 'تم تعليق الحساب - {{companyName}}'
            },
            body: {
              en: `Dear {{name}},

Your account with {{companyName}} has been temporarily suspended.

Reason: {{message}}
Date: {{timestamp}}

Please contact our support team for more information and to resolve this issue.

Best regards,
{{companyName}} Team`,
              ar: `عزيزي {{name}}،

تم تعليق حسابك في {{companyName}} مؤقتاً.

السبب: {{message}}
التاريخ: {{timestamp}}

يرجى الاتصال بفريق الدعم لدينا للحصول على مزيد من المعلومات وحل هذه المشكلة.

مع أطيب التحيات،
فريق {{companyName}}`
            }
          },
          systemAlert: {
            enabled: true,
            subject: {
              en: '{{alertType}} - {{companyName}}',
              ar: '{{alertType}} - {{companyName}}'
            },
            body: {
              en: `Dear {{name}},

We're writing to inform you about a system alert:

Alert Type: {{alertType}}
Message: {{message}}
Time: {{timestamp}}

Please take appropriate action if required.

Best regards,
{{companyName}} Team`,
              ar: `عزيزي {{name}}،

نكتب إليك لإعلامك بتنبيه النظام:

نوع التنبيه: {{alertType}}
الرسالة: {{message}}
الوقت: {{timestamp}}

يرجى اتخاذ الإجراء المناسب إذا لزم الأمر.

مع أطيب التحيات،
فريق {{companyName}}`
            }
          }
        },
        backup: {
          autoBackup: true,
          backupFrequency: 'daily',
          retentionDays: 30,
          backupLocation: '/backups'
        }
      };

      for (const [category, categorySettings] of Object.entries(defaultSettings)) {
        for (const [key, value] of Object.entries(categorySettings)) {
          const existingSetting = await prisma.systemSetting.findUnique({
            where: { key: `${category}.${key}` }
          });

          if (!existingSetting) {
            await this.setSetting(category, key, value);
          }
        }
      }

      return { success: true, message: 'Default settings initialized' };
    } catch (error) {
      throw new Error(`Failed to initialize default settings: ${error.message}`);
    }
  }

  /**
   * Determine if a setting should be public (accessible without authentication)
   */
  isPublicSetting(category, key) {
    const publicSettings = [
      'general.siteName',
      'general.siteDescription',
      'general.logo',
      'general.timezone',
      'general.language',
      'general.maintenanceMode',
      'company.name',
      'company.description',
      'company.address',
      'company.phone',
      'company.website',
      'company.businessHours',
      'company.socialMedia',
      'branding.primaryLogo',
      'branding.secondaryLogo',
      'branding.favicon',
      'branding.primaryColor',
      'branding.secondaryColor',
      'branding.accentColor',
      'branding.backgroundColor',
      'branding.textColor'
    ];

    return publicSettings.includes(`${category}.${key}`);
  }
}

module.exports = new SettingsService();
