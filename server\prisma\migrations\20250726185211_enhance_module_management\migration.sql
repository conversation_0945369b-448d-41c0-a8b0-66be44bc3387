-- AlterTable
ALTER TABLE "modules" ADD COLUMN     "author" VARCHAR(100) NOT NULL DEFAULT 'Unknown',
ADD COLUMN     "category" VARCHAR(50) NOT NULL DEFAULT 'general',
ADD COLUMN     "config" JSONB,
ADD COLUMN     "dependencies" JSONB,
ADD COLUMN     "filePath" VARCHAR(255),
ADD COLUMN     "health" INTEGER NOT NULL DEFAULT 100,
ADD COLUMN     "installedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "lastUpdate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "size" VARCHAR(20),
ADD COLUMN     "status" VARCHAR(20) NOT NULL DEFAULT 'inactive',
ADD COLUMN     "uploadedBy" TEXT,
ADD COLUMN     "version" VARCHAR(20) NOT NULL DEFAULT '1.0.0',
ALTER COLUMN "isActive" SET DEFAULT false;

-- AddF<PERSON><PERSON>Key
ALTER TABLE "modules" ADD CONSTRAINT "modules_uploadedBy_fkey" FOREIGN KEY ("uploadedBy") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
