const axios = require('axios');
const { PrismaClient } = require('@prisma/client');

const BASE_URL = 'http://localhost:3000';
const prisma = new PrismaClient();

async function testRoleMiddleware() {
  try {
    console.log('🛡️ اختبار middleware التحقق من الأدوار...');
    
    // تسجيل دخول المدير
    console.log('🔐 تسجيل دخول المدير...');
    const adminLogin = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password'
    });
    const adminToken = adminLogin.data.accessToken;
    console.log('✅ تم تسجيل دخول المدير بنجاح');
    
    // تسجيل دخول المستخدم العادي
    console.log('🔐 تسجيل دخول المستخدم العادي...');
    const userLogin = await axios.post(`${BASE_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password'
    });
    const userToken = userLogin.data.accessToken;
    console.log('✅ تم تسجيل دخول المستخدم العادي بنجاح');
    
    // اختبار 1: المدير يمكنه الوصول لإدارة الأدوار
    console.log('\n📋 اختبار 1: وصول المدير لإدارة الأدوار');
    try {
      const adminRolesResponse = await axios.get(`${BASE_URL}/api/roles`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log(`✅ المدير يمكنه الوصول - تم جلب ${adminRolesResponse.data.length} دور`);
    } catch (error) {
      console.error('❌ المدير لا يمكنه الوصول:', error.response?.data || error.message);
    }
    
    // اختبار 2: المستخدم العادي يمكنه الوصول لإدارة الأدوار (حالياً لا يوجد تقييد)
    console.log('\n👤 اختبار 2: وصول المستخدم العادي لإدارة الأدوار');
    try {
      const userRolesResponse = await axios.get(`${BASE_URL}/api/roles`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log(`✅ المستخدم العادي يمكنه الوصول - تم جلب ${userRolesResponse.data.length} دور`);
      console.log('⚠️ ملاحظة: حالياً لا يوجد تقييد على الأدوار في APIs');
    } catch (error) {
      console.error('❌ المستخدم العادي لا يمكنه الوصول:', error.response?.data || error.message);
    }
    
    // اختبار 3: إنشاء دور بواسطة المدير
    console.log('\n➕ اختبار 3: إنشاء دور بواسطة المدير');
    try {
      const newRole = {
        name: 'admin_test_role_' + Date.now(),
        description: 'دور اختبار من المدير',
        permissions: { test: ['read'] }
      };
      
      const createResponse = await axios.post(`${BASE_URL}/api/roles`, newRole, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log('✅ المدير يمكنه إنشاء الأدوار');
      
      // حذف الدور المؤقت
      await axios.delete(`${BASE_URL}/api/roles/${createResponse.data.id}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      
    } catch (error) {
      console.error('❌ المدير لا يمكنه إنشاء الأدوار:', error.response?.data || error.message);
    }
    
    // اختبار 4: إنشاء دور بواسطة المستخدم العادي
    console.log('\n👤 اختبار 4: إنشاء دور بواسطة المستخدم العادي');
    try {
      const newRole = {
        name: 'user_test_role_' + Date.now(),
        description: 'دور اختبار من المستخدم العادي',
        permissions: { test: ['read'] }
      };
      
      const createResponse = await axios.post(`${BASE_URL}/api/roles`, newRole, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log('⚠️ المستخدم العادي يمكنه إنشاء الأدوار (يجب تقييد هذا!)');
      
      // حذف الدور المؤقت
      await axios.delete(`${BASE_URL}/api/roles/${createResponse.data.id}`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      
    } catch (error) {
      console.error('✅ المستخدم العادي لا يمكنه إنشاء الأدوار (هذا صحيح):', error.response?.data || error.message);
    }
    
    // اختبار 5: فحص معلومات المستخدم الحالي
    console.log('\n🔍 اختبار 5: فحص معلومات المستخدمين');
    
    try {
      const adminMe = await axios.get(`${BASE_URL}/api/auth/me`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      console.log('👑 معلومات المدير:');
      console.log(`  البريد: ${adminMe.data.email}`);
      console.log(`  الأدوار: ${adminMe.data.roles?.map(r => r.role.name).join(', ') || 'لا توجد أدوار'}`);
      
      const userMe = await axios.get(`${BASE_URL}/api/auth/me`, {
        headers: { 'Authorization': `Bearer ${userToken}` }
      });
      console.log('👤 معلومات المستخدم العادي:');
      console.log(`  البريد: ${userMe.data.email}`);
      console.log(`  الأدوار: ${userMe.data.roles?.map(r => r.role.name).join(', ') || 'لا توجد أدوار'}`);
      
    } catch (error) {
      console.error('❌ خطأ في جلب معلومات المستخدم:', error.response?.data || error.message);
    }
    
    console.log('\n🎉 انتهى اختبار middleware الأدوار');
    
  } catch (error) {
    console.error('❌ خطأ عام في الاختبار:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testRoleMiddleware();
