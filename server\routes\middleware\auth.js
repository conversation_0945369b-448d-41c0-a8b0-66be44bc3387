const PrismaUserService = require('../../services/prismaUserService.js');
const jwt = require('jsonwebtoken');
const { checkTokenBlacklist } = require('../../utils/tokenBlacklist');

// Cache for user data to reduce database queries
const userCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

const requireUser = async (req, res, next) => {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ message: 'Unauthorized' });

  // Check if token is blacklisted (async but don't wait)
  checkTokenBlacklist(req, res, () => {}).catch(console.error);

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const userId = decoded.sub;

    // Check cache first
    const cacheKey = `user:${userId}`;
    const cached = userCache.get(cacheKey);

    if (cached && (Date.now() - cached.timestamp) < CACHE_TTL) {
      req.user = cached.user;
      return next();
    }

    // Fetch from database with optimized query
    const user = await PrismaUserService.get(userId);
    if (!user || !user.isActive) {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // Cache the user data
    userCache.set(cacheKey, {
      user,
      timestamp: Date.now()
    });

    // Clean up old cache entries periodically
    if (userCache.size > 1000) {
      const now = Date.now();
      for (const [key, value] of userCache.entries()) {
        if ((now - value.timestamp) > CACHE_TTL) {
          userCache.delete(key);
        }
      }
    }

    req.user = user;
    next();
  } catch (err) {
    return res.status(401).json({ error: 'Invalid or expired token' });
  }
};

// Alias for compatibility
const authenticateToken = requireUser;

module.exports = {
  requireUser,
  authenticateToken,
};
