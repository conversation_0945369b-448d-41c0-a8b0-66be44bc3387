/**
 * Audit Logging Middleware
 * تسجيل جميع الأنشطة الأمنية والحساسة
 */

const { prisma } = require('../../config/prisma');

/**
 * Log security events
 * تسجيل الأحداث الأمنية
 */
const logSecurityEvent = async (eventType, details, req, user = null) => {
  try {
    const logEntry = {
      action: eventType,
      resource: req.originalUrl,
      details: details,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.get('User-Agent') || 'Unknown',
      userId: user?.id || null
    };

    // Log to database
    await prisma.auditLog.create({
      data: logEntry
    });

    // Also log to console for immediate monitoring
    console.log(`[SECURITY] ${eventType}:`, {
      ...logEntry,
      details: details // Keep object format for console
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
};

/**
 * Authentication audit middleware
 * تسجيل أحداث المصادقة
 */
const auditAuth = (eventType) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log the authentication event
      const details = {
        email: req.body?.email,
        success: res.statusCode < 400,
        statusCode: res.statusCode,
        timestamp: new Date().toISOString()
      };

      // Don't wait for logging to complete
      logSecurityEvent(eventType, details, req).catch(console.error);
      
      return originalSend.call(this, data);
    };
    
    next();
  };
};

/**
 * Failed login attempt logger
 * تسجيل محاولات تسجيل الدخول الفاشلة
 */
const logFailedLogin = async (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    if (res.statusCode >= 400) {
      const details = {
        email: req.body?.email,
        reason: 'Invalid credentials',
        timestamp: new Date().toISOString(),
        attempts: 1 // Could be enhanced to track cumulative attempts
      };
      
      logSecurityEvent('FAILED_LOGIN', details, req).catch(console.error);
    }
    
    return originalSend.call(this, data);
  };
  
  next();
};

/**
 * Successful login logger
 * تسجيل عمليات تسجيل الدخول الناجحة
 */
const logSuccessfulLogin = async (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    if (res.statusCode < 400) {
      const details = {
        email: req.body?.email,
        timestamp: new Date().toISOString(),
        tokenGenerated: true
      };
      
      logSecurityEvent('SUCCESSFUL_LOGIN', details, req).catch(console.error);
    }
    
    return originalSend.call(this, data);
  };
  
  next();
};

/**
 * Permission denied logger
 * تسجيل محاولات الوصول المرفوضة
 */
const logPermissionDenied = (requiredPermission) => {
  return async (req, res, next) => {
    const originalStatus = res.status;
    
    res.status = function(code) {
      if (code === 403) {
        const details = {
          userId: req.user?.id,
          email: req.user?.email,
          requiredPermission,
          endpoint: req.originalUrl,
          timestamp: new Date().toISOString()
        };
        
        logSecurityEvent('PERMISSION_DENIED', details, req, req.user).catch(console.error);
      }
      
      return originalStatus.call(this, code);
    };
    
    next();
  };
};

/**
 * Suspicious activity detector
 * كاشف الأنشطة المشبوهة
 */
const detectSuspiciousActivity = async (req, res, next) => {
  const suspiciousPatterns = [
    /(\bor\b|\band\b).*=.*\d/i, // SQL injection patterns
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, // XSS patterns
    /javascript:/i, // JavaScript injection
    /\.\.\//g, // Path traversal
    /\bexec\b|\beval\b|\bsystem\b/i // Command injection
  ];
  
  const checkForSuspiciousContent = (obj) => {
    const content = JSON.stringify(obj);
    return suspiciousPatterns.some(pattern => pattern.test(content));
  };
  
  let suspicious = false;
  let suspiciousContent = [];
  
  if (req.body && checkForSuspiciousContent(req.body)) {
    suspicious = true;
    suspiciousContent.push('body');
  }
  
  if (req.query && checkForSuspiciousContent(req.query)) {
    suspicious = true;
    suspiciousContent.push('query');
  }
  
  if (req.params && checkForSuspiciousContent(req.params)) {
    suspicious = true;
    suspiciousContent.push('params');
  }
  
  if (suspicious) {
    const details = {
      suspiciousContent,
      endpoint: req.originalUrl,
      method: req.method,
      body: req.body,
      query: req.query,
      params: req.params,
      timestamp: new Date().toISOString()
    };
    
    await logSecurityEvent('SUSPICIOUS_ACTIVITY', details, req, req.user);
  }
  
  next();
};

/**
 * Rate limit exceeded logger
 * تسجيل تجاوز حدود المعدل
 */
const logRateLimitExceeded = async (req, res, next) => {
  const originalStatus = res.status;
  
  res.status = function(code) {
    if (code === 429) {
      const details = {
        endpoint: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
      };
      
      logSecurityEvent('RATE_LIMIT_EXCEEDED', details, req, req.user).catch(console.error);
    }
    
    return originalStatus.call(this, code);
  };
  
  next();
};

module.exports = {
  logSecurityEvent,
  auditAuth,
  logFailedLogin,
  logSuccessfulLogin,
  logPermissionDenied,
  detectSuspiciousActivity,
  logRateLimitExceeded
};
