const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const SettingsService = require('../services/SettingsService');
const { authenticateToken } = require('./middleware/auth');
const { checkRole } = require('./middleware/roleCheck');

// Configure multer for logo uploads
const storage = multer.memoryStorage();
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

/**
 * @swagger
 * components:
 *   schemas:
 *     SystemSettings:
 *       type: object
 *       properties:
 *         general:
 *           type: object
 *           properties:
 *             siteName:
 *               type: string
 *               example: "WebCore System"
 *             siteDescription:
 *               type: string
 *               example: "Advanced Web Management System"
 *             logo:
 *               type: string
 *               example: ""
 *             timezone:
 *               type: string
 *               example: "UTC"
 *             language:
 *               type: string
 *               example: "en"
 *             maintenanceMode:
 *               type: boolean
 *               example: false
 *         security:
 *           type: object
 *           properties:
 *             passwordMinLength:
 *               type: integer
 *               example: 8
 *             passwordRequireSpecial:
 *               type: boolean
 *               example: true
 *             sessionTimeout:
 *               type: integer
 *               example: 30
 *             maxLoginAttempts:
 *               type: integer
 *               example: 5
 *             twoFactorRequired:
 *               type: boolean
 *               example: false
 *             ipWhitelist:
 *               type: array
 *               items:
 *                 type: string
 *               example: []
 *         notifications:
 *           type: object
 *           properties:
 *             emailEnabled:
 *               type: boolean
 *               example: true
 *             smsEnabled:
 *               type: boolean
 *               example: false
 *             pushEnabled:
 *               type: boolean
 *               example: true
 *             adminEmail:
 *               type: string
 *               example: "<EMAIL>"
 *             smtpHost:
 *               type: string
 *               example: ""
 *             smtpPort:
 *               type: integer
 *               example: 587
 *             smtpUser:
 *               type: string
 *               example: ""
 *             smtpPassword:
 *               type: string
 *               example: ""
 *         backup:
 *           type: object
 *           properties:
 *             autoBackup:
 *               type: boolean
 *               example: true
 *             backupFrequency:
 *               type: string
 *               example: "daily"
 *             retentionDays:
 *               type: integer
 *               example: 30
 *             backupLocation:
 *               type: string
 *               example: "/backups"
 */

/**
 * @swagger
 * /api/settings:
 *   get:
 *     summary: Get all system settings
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   $ref: '#/components/schemas/SystemSettings'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const settings = await SettingsService.getAllSettings();
    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error getting settings:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/category/{category}:
 *   get:
 *     summary: Get settings by category
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *           enum: [general, security, notifications, backup]
 *         description: Settings category
 *     responses:
 *       200:
 *         description: Category settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/category/:category', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const { category } = req.params;
    const settings = await SettingsService.getSettingsByCategory(category);
    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error getting category settings:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings:
 *   put:
 *     summary: Update system settings
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SystemSettings'
 *     responses:
 *       200:
 *         description: Settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Settings updated successfully"
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.put('/', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const settingsData = req.body;
    
    if (!settingsData || typeof settingsData !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Invalid settings data'
      });
    }

    const result = await SettingsService.updateSettings(settingsData);
    res.json(result);
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/{category}/{key}:
 *   get:
 *     summary: Get a specific setting value
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *         description: Setting category
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: Setting key
 *     responses:
 *       200:
 *         description: Setting value retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: string
 *       404:
 *         description: Setting not found
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.get('/:category/:key', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const { category, key } = req.params;
    const value = await SettingsService.getSetting(category, key);
    
    if (value === null) {
      return res.status(404).json({
        success: false,
        message: 'Setting not found'
      });
    }

    res.json({
      success: true,
      data: value
    });
  } catch (error) {
    console.error('Error getting setting:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/{category}/{key}:
 *   put:
 *     summary: Set a specific setting value
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: category
 *         required: true
 *         schema:
 *           type: string
 *         description: Setting category
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: Setting key
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               value:
 *                 type: string
 *                 description: Setting value
 *               description:
 *                 type: string
 *                 description: Optional setting description
 *     responses:
 *       200:
 *         description: Setting updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Setting updated successfully"
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.put('/:category/:key', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const { category, key } = req.params;
    const { value, description } = req.body;
    
    if (value === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Value is required'
      });
    }

    await SettingsService.setSetting(category, key, value, description);
    res.json({
      success: true,
      message: 'Setting updated successfully'
    });
  } catch (error) {
    console.error('Error setting value:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/initialize:
 *   post:
 *     summary: Initialize default settings
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Default settings initialized successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Default settings initialized"
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.post('/initialize', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const result = await SettingsService.initializeDefaultSettings();
    res.json(result);
  } catch (error) {
    console.error('Error initializing settings:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/public:
 *   get:
 *     summary: Get public system settings (no authentication required)
 *     tags: [Settings]
 *     responses:
 *       200:
 *         description: Public settings retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     general:
 *                       type: object
 *                       properties:
 *                         siteName:
 *                           type: string
 *                           example: "WebCore System"
 *                         siteDescription:
 *                           type: string
 *                           example: "Advanced Web Management System"
 *                         logo:
 *                           type: string
 *                           example: ""
 *                         timezone:
 *                           type: string
 *                           example: "UTC"
 *                         language:
 *                           type: string
 *                           example: "en"
 *                         maintenanceMode:
 *                           type: boolean
 *                           example: false
 *       500:
 *         description: Server error
 */
router.get('/public', async (req, res) => {
  try {
    // Get only public settings (no authentication required)
    const publicSettings = await SettingsService.getSettingsByCategory('general');
    
    res.json({
      success: true,
      data: {
        general: publicSettings
      }
    });
  } catch (error) {
    console.error('Error getting public settings:', error);
    // Return default values on error
    res.json({
      success: true,
      data: {
        general: {
          siteName: 'WebCore System',
          siteDescription: 'Advanced Web Management System',
          logo: '',
          timezone: 'UTC',
          language: 'en',
          maintenanceMode: false
        }
      }
    });
  }
});

/**
 * @swagger
 * /api/settings/timezones:
 *   get:
 *     summary: Get list of available timezones
 *     tags: [Settings]
 *     responses:
 *       200:
 *         description: Timezones retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: string
 *                         example: "America/New_York"
 *                       label:
 *                         type: string
 *                         example: "Eastern Time (UTC-5)"
 *                       offset:
 *                         type: string
 *                         example: "UTC-5"
 */
router.get('/timezones', async (req, res) => {
  try {
    // Comprehensive list of timezones with proper formatting
    const timezones = [
      // UTC and GMT
      { value: 'UTC', label: 'Coordinated Universal Time (UTC+0)', offset: 'UTC+0' },
      { value: 'GMT', label: 'Greenwich Mean Time (GMT+0)', offset: 'GMT+0' },
      
      // Americas
      { value: 'America/New_York', label: 'Eastern Time (UTC-5)', offset: 'UTC-5' },
      { value: 'America/Chicago', label: 'Central Time (UTC-6)', offset: 'UTC-6' },
      { value: 'America/Denver', label: 'Mountain Time (UTC-7)', offset: 'UTC-7' },
      { value: 'America/Los_Angeles', label: 'Pacific Time (UTC-8)', offset: 'UTC-8' },
      { value: 'America/Anchorage', label: 'Alaska Time (UTC-9)', offset: 'UTC-9' },
      { value: 'Pacific/Honolulu', label: 'Hawaii Time (UTC-10)', offset: 'UTC-10' },
      { value: 'America/Toronto', label: 'Toronto (UTC-5)', offset: 'UTC-5' },
      { value: 'America/Vancouver', label: 'Vancouver (UTC-8)', offset: 'UTC-8' },
      { value: 'America/Mexico_City', label: 'Mexico City (UTC-6)', offset: 'UTC-6' },
      { value: 'America/Sao_Paulo', label: 'São Paulo (UTC-3)', offset: 'UTC-3' },
      { value: 'America/Buenos_Aires', label: 'Buenos Aires (UTC-3)', offset: 'UTC-3' },
      { value: 'America/Lima', label: 'Lima (UTC-5)', offset: 'UTC-5' },
      { value: 'America/Bogota', label: 'Bogotá (UTC-5)', offset: 'UTC-5' },
      
      // Europe
      { value: 'Europe/London', label: 'London (UTC+0)', offset: 'UTC+0' },
      { value: 'Europe/Paris', label: 'Paris (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Berlin', label: 'Berlin (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Rome', label: 'Rome (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Madrid', label: 'Madrid (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Amsterdam', label: 'Amsterdam (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Brussels', label: 'Brussels (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Vienna', label: 'Vienna (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Zurich', label: 'Zurich (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Stockholm', label: 'Stockholm (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Oslo', label: 'Oslo (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Copenhagen', label: 'Copenhagen (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Helsinki', label: 'Helsinki (UTC+2)', offset: 'UTC+2' },
      { value: 'Europe/Warsaw', label: 'Warsaw (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Prague', label: 'Prague (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Budapest', label: 'Budapest (UTC+1)', offset: 'UTC+1' },
      { value: 'Europe/Bucharest', label: 'Bucharest (UTC+2)', offset: 'UTC+2' },
      { value: 'Europe/Athens', label: 'Athens (UTC+2)', offset: 'UTC+2' },
      { value: 'Europe/Istanbul', label: 'Istanbul (UTC+3)', offset: 'UTC+3' },
      { value: 'Europe/Moscow', label: 'Moscow (UTC+3)', offset: 'UTC+3' },
      
      // Asia
      { value: 'Asia/Dubai', label: 'Dubai (UTC+4)', offset: 'UTC+4' },
      { value: 'Asia/Riyadh', label: 'Riyadh (UTC+3)', offset: 'UTC+3' },
      { value: 'Asia/Kuwait', label: 'Kuwait (UTC+3)', offset: 'UTC+3' },
      { value: 'Asia/Qatar', label: 'Qatar (UTC+3)', offset: 'UTC+3' },
      { value: 'Asia/Bahrain', label: 'Bahrain (UTC+3)', offset: 'UTC+3' },
      { value: 'Asia/Tehran', label: 'Tehran (UTC+3:30)', offset: 'UTC+3:30' },
      { value: 'Asia/Karachi', label: 'Karachi (UTC+5)', offset: 'UTC+5' },
      { value: 'Asia/Kolkata', label: 'Mumbai/Delhi (UTC+5:30)', offset: 'UTC+5:30' },
      { value: 'Asia/Dhaka', label: 'Dhaka (UTC+6)', offset: 'UTC+6' },
      { value: 'Asia/Bangkok', label: 'Bangkok (UTC+7)', offset: 'UTC+7' },
      { value: 'Asia/Singapore', label: 'Singapore (UTC+8)', offset: 'UTC+8' },
      { value: 'Asia/Kuala_Lumpur', label: 'Kuala Lumpur (UTC+8)', offset: 'UTC+8' },
      { value: 'Asia/Jakarta', label: 'Jakarta (UTC+7)', offset: 'UTC+7' },
      { value: 'Asia/Manila', label: 'Manila (UTC+8)', offset: 'UTC+8' },
      { value: 'Asia/Hong_Kong', label: 'Hong Kong (UTC+8)', offset: 'UTC+8' },
      { value: 'Asia/Shanghai', label: 'Beijing/Shanghai (UTC+8)', offset: 'UTC+8' },
      { value: 'Asia/Tokyo', label: 'Tokyo (UTC+9)', offset: 'UTC+9' },
      { value: 'Asia/Seoul', label: 'Seoul (UTC+9)', offset: 'UTC+9' },
      { value: 'Asia/Taipei', label: 'Taipei (UTC+8)', offset: 'UTC+8' },
      { value: 'Asia/Vladivostok', label: 'Vladivostok (UTC+10)', offset: 'UTC+10' },
      
      // Africa
      { value: 'Africa/Cairo', label: 'Cairo (UTC+2)', offset: 'UTC+2' },
      { value: 'Africa/Johannesburg', label: 'Johannesburg (UTC+2)', offset: 'UTC+2' },
      { value: 'Africa/Lagos', label: 'Lagos (UTC+1)', offset: 'UTC+1' },
      { value: 'Africa/Nairobi', label: 'Nairobi (UTC+3)', offset: 'UTC+3' },
      { value: 'Africa/Casablanca', label: 'Casablanca (UTC+1)', offset: 'UTC+1' },
      { value: 'Africa/Tunis', label: 'Tunis (UTC+1)', offset: 'UTC+1' },
      { value: 'Africa/Algiers', label: 'Algiers (UTC+1)', offset: 'UTC+1' },
      
      // Oceania
      { value: 'Australia/Sydney', label: 'Sydney (UTC+10)', offset: 'UTC+10' },
      { value: 'Australia/Melbourne', label: 'Melbourne (UTC+10)', offset: 'UTC+10' },
      { value: 'Australia/Brisbane', label: 'Brisbane (UTC+10)', offset: 'UTC+10' },
      { value: 'Australia/Perth', label: 'Perth (UTC+8)', offset: 'UTC+8' },
      { value: 'Australia/Adelaide', label: 'Adelaide (UTC+9:30)', offset: 'UTC+9:30' },
      { value: 'Pacific/Auckland', label: 'Auckland (UTC+12)', offset: 'UTC+12' },
      { value: 'Pacific/Fiji', label: 'Fiji (UTC+12)', offset: 'UTC+12' },
      
      // Additional important zones
      { value: 'Atlantic/Azores', label: 'Azores (UTC-1)', offset: 'UTC-1' },
      { value: 'Atlantic/Cape_Verde', label: 'Cape Verde (UTC-1)', offset: 'UTC-1' },
      { value: 'Indian/Mauritius', label: 'Mauritius (UTC+4)', offset: 'UTC+4' },
      { value: 'Pacific/Guam', label: 'Guam (UTC+10)', offset: 'UTC+10' }
    ];

    res.json({
      success: true,
      data: timezones
    });
  } catch (error) {
    console.error('Error getting timezones:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/upload-logo:
 *   post:
 *     summary: Upload logo file
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               logo:
 *                 type: string
 *                 format: binary
 *                 description: Logo image file
 *               type:
 *                 type: string
 *                 enum: [primary, secondary, favicon]
 *                 description: Type of logo
 *             required:
 *               - logo
 *               - type
 *     responses:
 *       200:
 *         description: Logo uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Logo uploaded successfully"
 *                 logoUrl:
 *                   type: string
 *                   example: "data:image/png;base64,..."
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.post('/upload-logo', authenticateToken, checkRole(['admin']), upload.single('logo'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No logo file provided'
      });
    }

    const { type } = req.body;
    if (!type || !['primary', 'secondary', 'favicon'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid logo type. Must be primary, secondary, or favicon'
      });
    }

    // Convert file to base64 data URL
    const base64 = req.file.buffer.toString('base64');
    const logoUrl = `data:${req.file.mimetype};base64,${base64}`;

    // Save to settings
    const settingKey = `${type}Logo`;
    await SettingsService.setSetting('branding', settingKey, logoUrl, `${type} logo image`);

    res.json({
      success: true,
      message: 'Logo uploaded successfully',
      logoUrl: logoUrl
    });
  } catch (error) {
    console.error('Error uploading logo:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

/**
 * @swagger
 * /api/settings/email-templates/preview:
 *   post:
 *     summary: Preview email template with sample data
 *     tags: [Settings]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               templateType:
 *                 type: string
 *                 example: "welcome"
 *               subject:
 *                 type: string
 *                 example: "Welcome to {{companyName}}!"
 *               body:
 *                 type: string
 *                 example: "Dear {{name}}, welcome to our platform!"
 *               language:
 *                 type: string
 *                 enum: [en, ar]
 *                 example: "en"
 *             required:
 *               - templateType
 *               - subject
 *               - body
 *               - language
 *     responses:
 *       200:
 *         description: Email template preview generated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 preview:
 *                   type: object
 *                   properties:
 *                     subject:
 *                       type: string
 *                       example: "Welcome to WebCore Company!"
 *                     body:
 *                       type: string
 *                       example: "Dear John Doe, welcome to our platform!"
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Server error
 */
router.post('/email-templates/preview', authenticateToken, checkRole(['admin']), async (req, res) => {
  try {
    const { templateType, subject, body, language = 'en' } = req.body;

    if (!templateType || !subject || !body) {
      return res.status(400).json({
        success: false,
        message: 'Template type, subject, and body are required'
      });
    }

    // Sample data for preview
    const sampleData = {
      name: language === 'ar' ? 'أحمد محمد' : 'John Doe',
      companyName: language === 'ar' ? 'شركة ويب كور' : 'WebCore Company',
      resetLink: 'https://webcore.com/reset-password?token=abc123',
      alertType: language === 'ar' ? 'تنبيه أمني' : 'Security Alert',
      message: language === 'ar' ? 'تم اكتشاف محاولات دخول فاشلة متعددة' : 'Multiple failed login attempts detected',
      timestamp: new Date().toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')
    };

    let previewSubject = subject;
    let previewBody = body;

    // Replace placeholders with sample data
    Object.keys(sampleData).forEach(key => {
      const placeholder = new RegExp(`{{${key}}}`, 'g');
      const value = sampleData[key];
      previewSubject = previewSubject.replace(placeholder, value);
      previewBody = previewBody.replace(placeholder, value);
    });

    res.json({
      success: true,
      preview: {
        subject: previewSubject,
        body: previewBody
      }
    });
  } catch (error) {
    console.error('Error previewing email template:', error);
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

module.exports = router;
