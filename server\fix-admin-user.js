const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

async function checkAndCreateAdmin() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 البحث عن المستخدم admin...');
    
    // البحث عن المستخدم admin
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (adminUser) {
      console.log('✅ المستخدم admin موجود:', adminUser.email);
      console.log('📧 البريد:', adminUser.email);
      console.log('🆔 المعرف:', adminUser.id);
      console.log('✅ نشط:', adminUser.isActive);
      
      // تحديث كلمة المرور
      const hashedPassword = await bcrypt.hash('admin123', 10);
      await prisma.user.update({
        where: { id: adminUser.id },
        data: { 
          password: hashedPassword,
          isActive: true 
        }
      });
      console.log('🔄 تم تحديث كلمة المرور إلى: admin123');
    } else {
      console.log('❌ المستخدم admin غير موجود، سيتم إنشاؤه...');
      
      const hashedPassword = await bcrypt.hash('admin123', 10);
      const newAdmin = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          isActive: true
        }
      });
      console.log('✅ تم إنشاء المستخدم admin:', newAdmin.email);
    }
    
    // التحقق من الأدوار
    console.log('🔍 التحقق من الأدوار...');
    const adminRole = await prisma.role.findUnique({
      where: { name: 'admin' }
    });
    
    if (adminRole) {
      const user = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      });
      
      const userRole = await prisma.userRole.findFirst({
        where: {
          userId: user.id,
          roleId: adminRole.id
        }
      });
      
      if (!userRole) {
        await prisma.userRole.create({
          data: {
            userId: user.id,
            roleId: adminRole.id
          }
        });
        console.log('✅ تم ربط المستخدم بدور admin');
      } else {
        console.log('✅ المستخدم مربوط بدور admin بالفعل');
      }
    } else {
      console.log('⚠️ دور admin غير موجود');
    }
    
    // اختبار تسجيل الدخول
    console.log('\n🧪 اختبار تسجيل الدخول...');
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    const isPasswordValid = await bcrypt.compare('admin123', testUser.password);
    console.log('🔐 كلمة المرور صحيحة:', isPasswordValid);
    
    console.log('\n✅ تم الانتهاء من إعداد المستخدم admin');
    console.log('📧 البريد: <EMAIL>');
    console.log('🔑 كلمة المرور: admin123');
    
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkAndCreateAdmin();
