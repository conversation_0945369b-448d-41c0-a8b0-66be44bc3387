/**
 * Simple Performance Test
 * اختبار أداء مبسط
 */

const request = require('supertest');
const app = require('./app');

async function testPerformance() {
  console.log('🚀 بدء اختبارات الأداء...\n');

  try {
    // Test 1: Login Performance
    console.log('1️⃣ اختبار أداء تسجيل الدخول...');
    const loginStart = Date.now();
    
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpass123'
      });

    const loginTime = Date.now() - loginStart;
    console.log(`   ⏱️  وقت تسجيل الدخول: ${loginTime}ms`);
    console.log(`   ✅ النتيجة: ${loginTime < 200 ? 'ممتاز' : loginTime < 500 ? 'جيد' : 'يحتاج تحسين'}`);
    
    if (loginResponse.status !== 200) {
      console.log(`   ❌ فشل تسجيل الدخول: ${loginResponse.status}`);
      console.log(`   📄 الاستجابة:`, loginResponse.body);
      return;
    }

    const token = loginResponse.body.data?.accessToken || loginResponse.body.accessToken;
    console.log(`   🔑 تم الحصول على التوكن بنجاح\n`);

    // Test 2: Token Verification Performance
    console.log('2️⃣ اختبار أداء التحقق من التوكن...');
    const verifyStart = Date.now();
    
    const verifyResponse = await request(app)
      .get('/api/auth/me')
      .set('Authorization', `Bearer ${token}`);

    const verifyTime = Date.now() - verifyStart;
    console.log(`   ⏱️  وقت التحقق من التوكن: ${verifyTime}ms`);
    console.log(`   ✅ النتيجة: ${verifyTime < 50 ? 'ممتاز' : verifyTime < 100 ? 'جيد' : 'يحتاج تحسين'}`);
    
    if (verifyResponse.status !== 200) {
      console.log(`   ❌ فشل التحقق من التوكن: ${verifyResponse.status}`);
      return;
    }
    console.log(`   ✅ تم التحقق من التوكن بنجاح\n`);

    // Test 3: Concurrent Requests Performance
    console.log('3️⃣ اختبار أداء الطلبات المتزامنة...');
    const concurrentStart = Date.now();
    
    const concurrentRequests = Array(10).fill().map(() =>
      request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)
    );

    const concurrentResponses = await Promise.all(concurrentRequests);
    const concurrentTime = Date.now() - concurrentStart;
    const avgTime = concurrentTime / 10;
    
    console.log(`   ⏱️  وقت 10 طلبات متزامنة: ${concurrentTime}ms`);
    console.log(`   ⏱️  متوسط الوقت لكل طلب: ${avgTime.toFixed(2)}ms`);
    console.log(`   ✅ النتيجة: ${avgTime < 100 ? 'ممتاز' : avgTime < 200 ? 'جيد' : 'يحتاج تحسين'}`);
    
    const successfulRequests = concurrentResponses.filter(res => res.status === 200).length;
    console.log(`   ✅ الطلبات الناجحة: ${successfulRequests}/10\n`);

    // Test 4: Memory Usage
    console.log('4️⃣ اختبار استخدام الذاكرة...');
    const memoryUsage = process.memoryUsage();
    const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const heapTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    
    console.log(`   💾 الذاكرة المستخدمة: ${heapUsedMB}MB`);
    console.log(`   💾 إجمالي الذاكرة: ${heapTotalMB}MB`);
    console.log(`   ✅ النتيجة: ${heapUsedMB < 100 ? 'ممتاز' : heapUsedMB < 200 ? 'جيد' : 'يحتاج تحسين'}\n`);

    // Test 5: Health Check Performance
    console.log('5️⃣ اختبار أداء فحص الصحة...');
    const healthStart = Date.now();
    
    const healthResponse = await request(app)
      .get('/api/metrics/health');

    const healthTime = Date.now() - healthStart;
    console.log(`   ⏱️  وقت فحص الصحة: ${healthTime}ms`);
    console.log(`   ✅ النتيجة: ${healthTime < 50 ? 'ممتاز' : healthTime < 100 ? 'جيد' : 'يحتاج تحسين'}`);
    
    if (healthResponse.status === 200) {
      console.log(`   🏥 حالة النظام: ${healthResponse.body.status}`);
      console.log(`   📊 معدل الاستجابة: ${healthResponse.body.requests?.averageResponseTime || 'غير متوفر'}ms`);
    }

    console.log('\n🎉 انتهت اختبارات الأداء بنجاح!');
    console.log('📊 ملخص النتائج:');
    console.log(`   - تسجيل الدخول: ${loginTime}ms`);
    console.log(`   - التحقق من التوكن: ${verifyTime}ms`);
    console.log(`   - الطلبات المتزامنة: ${avgTime.toFixed(2)}ms متوسط`);
    console.log(`   - استخدام الذاكرة: ${heapUsedMB}MB`);
    console.log(`   - فحص الصحة: ${healthTime}ms`);

  } catch (error) {
    console.error('❌ خطأ في اختبار الأداء:', error.message);
  }
}

// تشغيل الاختبار
testPerformance().then(() => {
  console.log('\n✅ تم الانتهاء من جميع الاختبارات');
  process.exit(0);
}).catch(error => {
  console.error('❌ فشل في تشغيل اختبارات الأداء:', error);
  process.exit(1);
});
