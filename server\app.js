/**
 * Express Application Configuration
 * Separated from server.js for testing purposes
 */

require("dotenv").config();
const express = require("express");
const cors = require("cors");
const compression = require("compression");

// Security middleware imports
const { securityHeaders, additionalSecurityHeaders, secureCorsOptions, sanitizeRequest } = require('./routes/middleware/security.js');
const { apiLimiter, speedLimiter } = require('./routes/middleware/rateLimiter.js');
const { logRateLimitExceeded } = require('./routes/middleware/auditLogger.js');
const { initializeBlacklist } = require('./utils/tokenBlacklist.js');
const { monitorRequestPerformance } = require('./routes/middleware/performanceMonitor.js');

// Import routes
const basicRoutes = require("./routes/index");
const authRoutes = require("./routes/authRoutes");
const userRoutes = require("./routes/userRoutes");
const roleRoutes = require("./routes/roleRoutes");
const dashboardRoutes = require("./routes/dashboardRoutes");
const settingsRoutes = require("./routes/settingsRoutes");
const moduleRoutes = require("./routes/moduleRoutes");
const metricsRoutes = require("./routes/metricsRoutes");

// Import configurations
const { connectDB } = require("./config/prisma");
const { specs, swaggerUi } = require('./config/swagger');

/**
 * Configure CORS options for the application
 * @returns {Object} CORS configuration object
 */
function configureCors() {
  return secureCorsOptions;
}

/**
 * Configure basic middleware for the application
 * @param {Express} app - Express application instance
 */
function configureMiddleware(app) {
  // Compression first for better performance
  app.use(compression({
    filter: (req, res) => {
      // Don't compress responses with this request header
      if (req.headers['x-no-compression']) {
        return false;
      }
      // Fallback to standard filter function
      return compression.filter(req, res);
    },
    level: 6, // Compression level (1-9, 6 is good balance)
    threshold: 1024 // Only compress responses larger than 1KB
  }));

  // Performance monitoring (early in the chain)
  app.use(monitorRequestPerformance);

  // Security headers
  app.use(securityHeaders);
  app.use(additionalSecurityHeaders);

  // Rate limiting and monitoring
  app.use(apiLimiter);
  app.use(speedLimiter);
  app.use(logRateLimitExceeded);

  // Request sanitization
  app.use(sanitizeRequest);

  // CORS configuration
  app.use(cors(configureCors()));

  // Body parsing with size limits
  app.use(express.json({
    limit: '10mb',
    strict: true,
    type: 'application/json'
  }));
  app.use(express.urlencoded({
    extended: true,
    limit: '10mb',
    parameterLimit: 1000
  }));
}

/**
 * Configure application routes
 * @param {Express} app - Express application instance
 */
function configureRoutes(app) {
  app.use(basicRoutes);
  app.use('/api/auth', authRoutes);
  app.use('/api/users', userRoutes);
  app.use('/api/roles', roleRoutes);
  app.use('/api/dashboard', dashboardRoutes);
  app.use('/api/settings', settingsRoutes);
  app.use('/api/modules', moduleRoutes);
  app.use('/api/metrics', metricsRoutes);
}

/**
 * Configure error handlers for the application
 * @param {Express} app - Express application instance
 */
function configureErrorHandlers(app) {
  // 404 handler
  app.use((req, res, next) => {
    res.status(404).json({
      success: false,
      message: "Endpoint not found",
      path: req.path,
      method: req.method
    });
  });

  // Global error handler
  app.use((err, req, res, next) => {
    console.error(`Unhandled application error: ${err.message}`);
    console.error(err.stack);

    const isDevelopment = process.env.NODE_ENV === 'development';

    res.status(err.status || 500).json({
      success: false,
      message: isDevelopment ? err.message : "Internal server error",
      ...(isDevelopment && { stack: err.stack })
    });
  });
}

/**
 * Create and configure Express application
 * @returns {Express} Configured Express app
 */
function createApp() {
  const app = express();

  // Application settings
  app.enable('json spaces');
  app.enable('strict routing');

  configureMiddleware(app);

  // Database connection (only if not in test environment)
  if (process.env.NODE_ENV !== 'test') {
    connectDB();
    // Initialize security systems
    initializeBlacklist().then(() => {
      console.log('🛡️ Security systems initialized');
    }).catch(console.error);
  }

  // Error handling for app
  app.on("error", (error) => {
    console.error(`Server error: ${error.message}`);
    console.error(error.stack);
  });

  // Swagger Documentation
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'WebCore API Documentation'
  }));

  configureRoutes(app);
  configureErrorHandlers(app);

  return app;
}

module.exports = createApp();
