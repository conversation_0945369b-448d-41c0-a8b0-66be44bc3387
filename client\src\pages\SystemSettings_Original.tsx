import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import {
  Settings,
  Shield,
  Globe,
  Bell,
  Database,
  Upload,
  Save,
  RefreshCw,
  Image as ImageIcon,
  X,
  Mail,
  FileText,
  Eye,
  Send,
  Badge
} from "lucide-react"
import { getSystemSettings, updateSystemSettings } from "@/api/settings"
import { toast } from "@/hooks/useToast"
import { useLanguage } from "@/contexts/LanguageContext"
import { useSettings } from "@/contexts/SettingsContext"

interface SystemSettings {
  general: {
    siteName: string
    siteDescription: string
    logo: string
    timezone: string
    language: string
    maintenanceMode: boolean
  }
  security: {
    passwordMinLength: number
    passwordRequireSpecial: boolean
    sessionTimeout: number
    maxLoginAttempts: number
    twoFactorRequired: boolean
    ipWhitelist: string[]
  }
  notifications: {
    emailEnabled: boolean
    smsEnabled: boolean
    pushEnabled: boolean
    adminEmail: string
    smtpHost: string
    smtpPort: number
    smtpUser: string
    smtpPassword: string
  }
  backup: {
    autoBackup: boolean
    backupFrequency: string
    retentionDays: number
    backupLocation: string
  }
}

export function SystemSettings() {
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [logoFile, setLogoFile] = useState<File | null>(null)
  const [logoPreview, setLogoPreview] = useState<string>("")  
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false)
  const [previewTemplate, setPreviewTemplate] = useState<any>(null)
  const [previewData, setPreviewData] = useState<any>(null)
  const { t } = useLanguage()
  const { updateLogo, updateSiteName } = useSettings()

  // Helper function to get template display names
  const getTemplateDisplayName = (templateKey: string) => {
    const templateNames: { [key: string]: string } = {
      welcomeEmail: 'Welcome Email',
      passwordReset: 'Password Reset',
      accountSuspended: 'Account Suspended',
      systemAlert: 'System Alert'
    }
    return templateNames[templateKey] || templateKey
  }

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      console.log('Fetching system settings')
      const data = await getSystemSettings()
      setSettings(data.settings)
      if (data.settings.general.logo) {
        setLogoPreview(data.settings.general.logo)
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      toast({
        title: "Error",
        description: "Failed to load system settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    if (!settings) return

    setSaving(true)
    try {
      console.log('Saving system settings:', settings)
      await updateSystemSettings(settings)

      // Update the global settings context
      updateLogo(settings.general.logo)
      updateSiteName(settings.general.siteName)

      toast({
        title: "Success",
        description: "System settings updated successfully",
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: "Error",
        description: "Failed to save system settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const updateSetting = (section: keyof SystemSettings, key: string, value: any) => {
    if (!settings) return
    setSettings({
      ...settings,
      [section]: {
        ...settings[section],
        [key]: value
      }
    })
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: "Error",
          description: "File size must be less than 5MB",
          variant: "destructive",
        })
        return
      }

      if (!file.type.startsWith('image/')) {
        toast({
          title: "Error",
          description: "Please select a valid image file",
          variant: "destructive",
        })
        return
      }

      setLogoFile(file)
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setLogoPreview(result)
        updateSetting('general', 'logo', result)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeLogo = () => {
    setLogoFile(null)
    setLogoPreview("")
    updateSetting('general', 'logo', "")
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-slate-200 rounded w-48 mb-2"></div>
          <div className="h-4 bg-slate-200 rounded w-96"></div>
        </div>
        <div className="grid gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-slate-200 rounded w-32"></div>
                <div className="h-4 bg-slate-200 rounded w-64"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="h-4 bg-slate-200 rounded w-full"></div>
                  <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (!settings) return null

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-600 bg-clip-text text-transparent dark:from-slate-100 dark:to-slate-400">
            {t('systemSettings')}
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            {t('configureSystemPreferences')}
          </p>
        </div>
        <Button
          onClick={handleSaveSettings}
          disabled={saving}
          className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
        >
          {saving ? (
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Save className="mr-2 h-4 w-4" />
          )}
          {saving ? t('saving') : t('saveChanges')}
        </Button>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            {t('general')}
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            {t('security')}
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            {t('notifications')}
          </TabsTrigger>
          <TabsTrigger value="backup" className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            {t('backup')}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="general">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                {t('generalSettings')}
              </CardTitle>
              <CardDescription>
                {t('configureBasicInfo')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Logo Upload Section */}
              <div className="space-y-4">
                <Label>{t('companyLogo')}</Label>
                <div className="flex items-center gap-4">
                  {logoPreview ? (
                    <div className="relative">
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="w-20 h-20 object-contain rounded-lg border-2 border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none';
                          e.currentTarget.nextElementSibling.style.display = 'flex';
                        }}
                      />
                      <div className="w-20 h-20 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg items-center justify-center bg-slate-50 dark:bg-slate-800 hidden">
                        <ImageIcon className="h-8 w-8 text-slate-400" />
                      </div>
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                        onClick={removeLogo}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div className="w-20 h-20 border-2 border-dashed border-slate-300 dark:border-slate-600 rounded-lg flex items-center justify-center bg-slate-50 dark:bg-slate-800">
                      <ImageIcon className="h-8 w-8 text-slate-400" />
                    </div>
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="logo-upload" className="cursor-pointer">
                      <Button variant="outline" className="flex items-center gap-2" asChild>
                        <span>
                          <Upload className="h-4 w-4" />
                          {t('uploadLogo')}
                        </span>
                      </Button>
                    </Label>
                    <Input
                      id="logo-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                    />
                    <p className="text-xs text-slate-500">
                      PNG, JPG, GIF up to 5MB
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="siteName">{t('siteName')}</Label>
                  <Input
                    id="siteName"
                    value={settings.general.siteName}
                    onChange={(e) => updateSetting('general', 'siteName', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">{t('timezone')}</Label>
                  <Select
                    value={settings.general.timezone}
                    onValueChange={(value) => updateSetting('general', 'timezone', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white dark:bg-slate-800">
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="Asia/Riyadh">{t('riyadhTime')}</SelectItem>
                      <SelectItem value="Asia/Dubai">{t('dubaiTime')}</SelectItem>
                      <SelectItem value="Asia/Kuwait">{t('kuwaitTime')}</SelectItem>
                      <SelectItem value="Africa/Cairo">{t('cairoTime')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="siteDescription">{t('siteDescription')}</Label>
                <Textarea
                  id="siteDescription"
                  value={settings.general.siteDescription}
                  onChange={(e) => updateSetting('general', 'siteDescription', e.target.value)}
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="language">{t('defaultLanguage')}</Label>
                <Select
                  value={settings.general.language}
                  onValueChange={(value) => updateSetting('general', 'language', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="bg-white dark:bg-slate-800">
                    <SelectItem value="ar">{t('arabic')}</SelectItem>
                    <SelectItem value="en">{t('english')}</SelectItem>
                    <SelectItem value="es">{t('spanish')}</SelectItem>
                    <SelectItem value="fr">{t('french')}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('maintenanceMode')}</Label>
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    {t('enableMaintenanceMode')}
                  </div>
                </div>
                <Switch
                  checked={settings.general.maintenanceMode}
                  onCheckedChange={(checked) => updateSetting('general', 'maintenanceMode', checked)}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {t('securitySettings')}
              </CardTitle>
              <CardDescription>
                {t('configureSecurityPolicies')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">{t('minPasswordLength')}</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    value={settings.security.passwordMinLength}
                    onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">{t('maxLoginAttempts')}</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sessionTimeout">{t('sessionTimeout')}</Label>
                <Input
                  id="sessionTimeout"
                  type="number"
                  value={settings.security.sessionTimeout}
                  onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>{t('requireSpecialChars')}</Label>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {t('passwordsRequireSpecial')}
                    </div>
                  </div>
                  <Switch
                    checked={settings.security.passwordRequireSpecial}
                    onCheckedChange={(checked) => updateSetting('security', 'passwordRequireSpecial', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>{t('twoFactorRequired')}</Label>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {t('requireTwoFactor')}
                    </div>
                  </div>
                  <Switch
                    checked={settings.security.twoFactorRequired}
                    onCheckedChange={(checked) => updateSetting('security', 'twoFactorRequired', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="ipWhitelist">{t('ipWhitelist')}</Label>
                <Textarea
                  id="ipWhitelist"
                  value={settings.security.ipWhitelist.join('\n')}
                  onChange={(e) => updateSetting('security', 'ipWhitelist', e.target.value.split('\n').filter(ip => ip.trim()))}
                  rows={4}
                  placeholder="***********/24&#10;10.0.0.0/8"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                {t('notificationSettings')}
              </CardTitle>
              <CardDescription>
                {t('configureNotificationChannels')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>{t('emailNotifications')}</Label>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {t('enableEmailNotifications')}
                    </div>
                  </div>
                  <Switch
                    checked={settings.notifications.emailEnabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'emailEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>{t('smsNotifications')}</Label>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {t('enableSmsNotifications')}
                    </div>
                  </div>
                  <Switch
                    checked={settings.notifications.smsEnabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'smsEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>{t('pushNotifications')}</Label>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {t('enablePushNotifications')}
                    </div>
                  </div>
                  <Switch
                    checked={settings.notifications.pushEnabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'pushEnabled', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="adminEmail">{t('adminEmail')}</Label>
                  <Input
                    id="adminEmail"
                    type="email"
                    value={settings.notifications.adminEmail}
                    onChange={(e) => updateSetting('notifications', 'adminEmail', e.target.value)}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="smtpHost">{t('smtpHost')}</Label>
                    <Input
                      id="smtpHost"
                      value={settings.notifications.smtpHost}
                      onChange={(e) => updateSetting('notifications', 'smtpHost', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtpPort">{t('smtpPort')}</Label>
                    <Input
                      id="smtpPort"
                      type="number"
                      value={settings.notifications.smtpPort}
                      onChange={(e) => updateSetting('notifications', 'smtpPort', parseInt(e.target.value))}
                    />
                  </div>
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="smtpUser">{t('smtpUser')}</Label>
                    <Input
                      id="smtpUser"
                      value={settings.notifications.smtpUser}
                      onChange={(e) => updateSetting('notifications', 'smtpUser', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtpPassword">{t('smtpPassword')}</Label>
                    <Input
                      id="smtpPassword"
                      type="password"
                      value={settings.notifications.smtpPassword}
                      onChange={(e) => updateSetting('notifications', 'smtpPassword', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="email-templates">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Notification Templates
              </CardTitle>
              <CardDescription>
                Configure email templates for system notifications and user communications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {Object.entries(settings.emailTemplates).map(([templateKey, template]) => (
                <Card key={templateKey} className="border border-slate-200 dark:border-slate-700">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
                          <FileText className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{getTemplateDisplayName(templateKey)}</CardTitle>
                          <CardDescription className="text-sm">
                            {templateKey === 'welcomeEmail' && 'Sent when a new user account is created'}
                            {templateKey === 'passwordReset' && 'Sent when user requests password reset'}
                            {templateKey === 'accountSuspended' && 'Sent when user account is suspended'}
                            {templateKey === 'systemAlert' && 'Sent for system alerts and notifications'}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={template.enabled ? "default" : "secondary"}>
                          {template.enabled ? "Enabled" : "Disabled"}
                        </Badge>
                        <Switch
                          checked={template.enabled}
                          onCheckedChange={(checked) => updateEmailTemplate(templateKey, 'enabled', checked)}
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor={`${templateKey}-subject`}>Email Subject</Label>
                      <Input
                        id={`${templateKey}-subject`}
                        value={template.subject}
                        onChange={(e) => updateEmailTemplate(templateKey, 'subject', e.target.value)}
                        placeholder="Enter email subject..."
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`${templateKey}-body`}>Email Body</Label>
                      <Textarea
                        id={`${templateKey}-body`}
                        value={template.body}
                        onChange={(e) => updateEmailTemplate(templateKey, 'body', e.target.value)}
                        rows={8}
                        placeholder="Enter email body content..."
                        className="font-mono text-sm"
                      />
                      <div className="text-xs text-slate-500 space-y-1">
                        <p>Available placeholders:</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline" className="text-xs">{'{{name}}'}</Badge>
                          {templateKey === 'passwordReset' && (
                            <Badge variant="outline" className="text-xs">{'{{resetLink}}'}</Badge>
                          )}
                          {templateKey === 'systemAlert' && (
                            <>
                              <Badge variant="outline" className="text-xs">{'{{alertType}}'}</Badge>
                              <Badge variant="outline" className="text-xs">{'{{message}}'}</Badge>
                              <Badge variant="outline" className="text-xs">{'{{timestamp}}'}</Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePreviewTemplate(templateKey)}
                        className="flex items-center gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        Preview Template
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backup">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                {t('backupSettings')}
              </CardTitle>
              <CardDescription>
                {t('configureBackupPolicies')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>{t('autoBackup')}</Label>
                  <div className="text-sm text-slate-600 dark:text-slate-400">
                    {t('enableAutoBackup')}
                  </div>
                </div>
                <Switch
                  checked={settings.backup.autoBackup}
                  onCheckedChange={(checked) => updateSetting('backup', 'autoBackup', checked)}
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="backupFrequency">{t('backupFrequency')}</Label>
                  <Select
                    value={settings.backup.backupFrequency}
                    onValueChange={(value) => updateSetting('backup', 'backupFrequency', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white dark:bg-slate-800">
                      <SelectItem value="daily">{t('daily')}</SelectItem>
                      <SelectItem value="weekly">{t('weekly')}</SelectItem>
                      <SelectItem value="monthly">{t('monthly')}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="retentionDays">{t('retentionDays')}</Label>
                  <Input
                    id="retentionDays"
                    type="number"
                    value={settings.backup.retentionDays}
                    onChange={(e) => updateSetting('backup', 'retentionDays', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="backupLocation">{t('backupLocation')}</Label>
                <Input
                  id="backupLocation"
                  value={settings.backup.backupLocation}
                  onChange={(e) => updateSetting('backup', 'backupLocation', e.target.value)}
                  placeholder="/var/backups/webvue"
                />
              </div>

              <Separator />

              <div className="flex gap-2">
                <Button variant="outline">
                  <Upload className="mr-2 h-4 w-4" />
                  {t('createBackupNow')}
                </Button>
                <Button variant="outline">
                  <Database className="mr-2 h-4 w-4" />
                  {t('restoreFromBackup')}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Email Template Preview Dialog */}
      <Dialog open={previewDialogOpen} onOpenChange={setPreviewDialogOpen}>
        <DialogContent className="bg-white dark:bg-slate-900 max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Email Template Preview - {previewTemplate && getTemplateDisplayName(previewTemplate)}
            </DialogTitle>
            <DialogDescription>
              Preview of how the email will appear to recipients
            </DialogDescription>
          </DialogHeader>
          {previewData && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Subject:</Label>
                <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-lg border">
                  <p className="font-medium">{previewData.subject}</p>
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Email Body:</Label>
                <div className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg border max-h-96 overflow-y-auto">
                  <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                    {previewData.body}
                  </pre>
                </div>
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setPreviewDialogOpen(false)}>
                  Close
                </Button>
                <Button className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600">
                  <Send className="mr-2 h-4 w-4" />
                  Send Test Email
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}