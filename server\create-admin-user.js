/**
 * Create Admin User for Frontend Testing
 * إنشاء مستخدم إداري لاختبار الواجهة الأمامية
 */

const bcrypt = require('bcrypt');
const { prisma } = require('./config/prisma');

async function createAdminUser() {
  try {
    console.log('🔧 إنشاء مستخدم إداري...');

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 10);

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('👤 المستخدم الإداري موجود بالفعل، سيتم تحديث كلمة المرور...');
      
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: { 
          password: hashedPassword,
          isActive: true
        }
      });
    } else {
      console.log('👤 إنشاء مستخدم إداري جديد...');
      
      await prisma.user.create({
        data: {
          email: '<EMAIL>',
          password: hashedPassword,
          isActive: true
        }
      });
    }

    console.log('✅ تم إنشاء/تحديث المستخدم الإداري بنجاح');
    console.log('📧 البريد الإلكتروني: <EMAIL>');
    console.log('🔑 كلمة المرور: admin123');

  } catch (error) {
    console.error('❌ خطأ في إنشاء المستخدم الإداري:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createAdminUser();
