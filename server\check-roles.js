const { PrismaClient } = require('@prisma/client');

async function checkRoles() {
  const prisma = new PrismaClient();
  try {
    console.log('🔍 فحص الأدوار في قاعدة البيانات...');
    
    const roles = await prisma.role.findMany({
      include: {
        users: {
          include: {
            user: {
              select: { id: true, email: true }
            }
          }
        }
      }
    });
    
    console.log('📊 الأدوار الموجودة:');
    roles.forEach(role => {
      console.log(`- الدور: ${role.name}`);
      console.log(`  الوصف: ${role.description}`);
      console.log(`  الصلاحيات: ${JSON.stringify(role.permissions, null, 2)}`);
      console.log(`  عدد المستخدمين: ${role.users.length}`);
      role.users.forEach(userRole => {
        console.log(`    - ${userRole.user.email}`);
      });
      console.log('---');
    });
    
    const users = await prisma.user.findMany({
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });
    
    console.log('👥 المستخدمون وأدوارهم:');
    users.forEach(user => {
      console.log(`- المستخدم: ${user.email}`);
      console.log(`  نشط: ${user.isActive}`);
      console.log(`  الأدوار: ${user.roles.map(r => r.role.name).join(', ')}`);
      console.log('---');
    });
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRoles();
