const { checkRole, checkPermission, requireAdmin } = require('../routes/middleware/roleCheck');
const PrismaUserService = require('../services/prismaUserService');
const { setupTestDatabase } = require('./setup');

// Mock response object
const mockResponse = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

// Mock next function
const mockNext = jest.fn();

describe('Role Check Middleware', () => {
  let testData;

  beforeEach(async () => {
    testData = await setupTestDatabase();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkRole middleware', () => {
    test('should allow access for user with required role', async () => {
      const req = {
        user: { id: testData.adminUser.id }
      };
      const res = mockResponse();
      const middleware = checkRole(['admin']);

      await middleware(req, res, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(req.userRoles).toContain('admin');
    });

    test('should deny access for user without required role', async () => {
      const req = {
        user: { id: testData.regularUser.id }
      };
      const res = mockResponse();
      const middleware = checkRole(['admin']);

      await middleware(req, res, mockNext);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Access denied. Required role: admin'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should deny access for unauthenticated user', async () => {
      const req = {};
      const res = mockResponse();
      const middleware = checkRole(['admin']);

      await middleware(req, res, mockNext);

      expect(res.status).toHaveBeenCalledWith(401);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Authentication required'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('checkPermission middleware', () => {
    test('should allow access for user with required permission', async () => {
      const req = {
        user: { id: testData.adminUser.id }
      };
      const res = mockResponse();
      const middleware = checkPermission('roles', 'create');

      await middleware(req, res, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(req.userRoles).toContain('admin');
    });

    test('should deny access for user without required permission', async () => {
      const req = {
        user: { id: testData.regularUser.id }
      };
      const res = mockResponse();
      const middleware = checkPermission('roles', 'create');

      await middleware(req, res, mockNext);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Access denied. Required permission: roles.create'
      });
      expect(mockNext).not.toHaveBeenCalled();
    });

    test('should allow access for user with read permission', async () => {
      const req = {
        user: { id: testData.adminUser.id }
      };
      const res = mockResponse();
      const middleware = checkPermission('users', 'read');

      await middleware(req, res, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(req.userRoles).toContain('admin');
    });
  });

  describe('requireAdmin middleware', () => {
    test('should allow access for admin user', async () => {
      const req = {
        user: { id: testData.adminUser.id }
      };
      const res = mockResponse();

      await requireAdmin(req, res, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(req.userRoles).toContain('admin');
    });

    test('should deny access for non-admin user', async () => {
      const req = {
        user: { id: testData.regularUser.id }
      };
      const res = mockResponse();

      await requireAdmin(req, res, mockNext);

      expect(res.status).toHaveBeenCalledWith(403);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
