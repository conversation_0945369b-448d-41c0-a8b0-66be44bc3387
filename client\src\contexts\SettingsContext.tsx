import React, { createContext, useContext, useState, useEffect } from 'react'
import { getPublicSettings } from '@/api/settings'

interface SettingsContextType {
  logo: string
  siteName: string
  updateLogo: (logo: string) => void
  updateSiteName: (siteName: string) => void
  refreshSettings: () => Promise<void>
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [logo, setLogo] = useState<string>('')
  const [siteName, setSiteName] = useState<string>('WebCore System')

  const refreshSettings = async () => {
    try {
      const data = await getPublicSettings()
      // Only set logo if it's a valid data URL, don't try to load external URLs that might fail
      const logoUrl = data.general?.logo || ''
      if (logoUrl && logoUrl.startsWith('data:')) {
        setLogo(logoUrl)
      } else {
        setLogo('')
      }
      setSiteName(data.general?.siteName || 'WebCore System')
    } catch (error) {
      console.error('Error fetching public settings:', error)
      // Set default values on error
      setLogo('')
      setSiteName('WebCore System')
    }
  }

  useEffect(() => {
    refreshSettings()
  }, [])

  const updateLogo = (newLogo: string) => {
    setLogo(newLogo)
  }

  const updateSiteName = (newSiteName: string) => {
    setSiteName(newSiteName)
  }

  return (
    <SettingsContext.Provider value={{
      logo,
      siteName,
      updateLogo,
      updateSiteName,
      refreshSettings
    }}>
      {children}
    </SettingsContext.Provider>
  )
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}