<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم صفحة الدخول الجديدة</title>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <style>
        body { 
            font-family: 'IBM Plex Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
            min-height: 100vh;
            color: #1e293b;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border-radius: 15px;
            color: white;
        }
        .test-section {
            background: rgba(248, 250, 252, 0.8);
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            border: 1px solid #e2e8f0;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            color: white;
            font-size: 24px;
        }
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 10px;
        }
        .feature-desc {
            color: #64748b;
            line-height: 1.6;
        }
        .demo-section {
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            padding: 30px;
            border-radius: 15px;
            margin: 30px 0;
            text-align: center;
        }
        .demo-button {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            margin: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: right;
            border-bottom: 1px solid #e2e8f0;
        }
        .comparison-table th {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            font-weight: 600;
            color: #1e293b;
        }
        .old-design {
            color: #ef4444;
        }
        .new-design {
            color: #10b981;
            font-weight: 600;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }
        .status-improved {
            background: #dcfce7;
            color: #166534;
        }
        .status-new {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .status-enhanced {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 تصميم صفحة تسجيل الدخول الجديدة</h1>
            <p>تصميم متطور يتوافق مع نظام الداشبورد مع دعم كامل للغة العربية والإنجليزية</p>
        </div>

        <div class="demo-section">
            <h2>🚀 جرب التصميم الجديد الآن</h2>
            <p>اضغط على الرابط أدناه لمشاهدة صفحة تسجيل الدخول الجديدة</p>
            <a href="http://localhost:5173/login" class="demo-button" target="_blank">
                🔗 فتح صفحة تسجيل الدخول الجديدة
            </a>
        </div>

        <div class="test-section">
            <h3>✨ الميزات الجديدة</h3>
            <div class="feature-grid">
                <div class="feature-card">
                    <div class="feature-icon">🌍</div>
                    <div class="feature-title">دعم اللغات المتعدد</div>
                    <div class="feature-desc">تبديل فوري بين العربية والإنجليزية مع تغيير اتجاه النص تلقائياً</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">تصميم متوافق مع الداشبورد</div>
                    <div class="feature-desc">نفس الألوان والتدرجات والعناصر المستخدمة في الداشبورد</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🌙</div>
                    <div class="feature-title">الوضع الليلي والنهاري</div>
                    <div class="feature-desc">تبديل سلس بين الوضعين مع حفظ التفضيل</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <div class="feature-title">أمان محسن</div>
                    <div class="feature-desc">إظهار/إخفاء كلمة المرور مع رسائل خطأ واضحة</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">تصميم متجاوب</div>
                    <div class="feature-desc">يعمل بشكل مثالي على جميع الأجهزة والشاشات</div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">تأثيرات بصرية متقدمة</div>
                    <div class="feature-desc">خلفيات متحركة وتأثيرات blur وانتقالات سلسة</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 مقارنة بين التصميم القديم والجديد</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>الميزة</th>
                        <th>التصميم القديم</th>
                        <th>التصميم الجديد</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>دعم اللغات</td>
                        <td class="old-design">الإنجليزية فقط</td>
                        <td class="new-design">عربي + إنجليزي مع RTL</td>
                        <td><span class="status-badge status-new">جديد</span></td>
                    </tr>
                    <tr>
                        <td>التصميم</td>
                        <td class="old-design">بسيط وعادي</td>
                        <td class="new-design">متطور مع تدرجات وتأثيرات</td>
                        <td><span class="status-badge status-improved">محسن</span></td>
                    </tr>
                    <tr>
                        <td>التوافق مع الداشبورد</td>
                        <td class="old-design">غير متوافق</td>
                        <td class="new-design">متوافق بالكامل</td>
                        <td><span class="status-badge status-enhanced">معزز</span></td>
                    </tr>
                    <tr>
                        <td>رسائل الخطأ</td>
                        <td class="old-design">بالإنجليزية فقط</td>
                        <td class="new-design">متعددة اللغات ومحسنة</td>
                        <td><span class="status-badge status-improved">محسن</span></td>
                    </tr>
                    <tr>
                        <td>إظهار كلمة المرور</td>
                        <td class="old-design">غير متوفر</td>
                        <td class="new-design">متوفر مع أيقونات</td>
                        <td><span class="status-badge status-new">جديد</span></td>
                    </tr>
                    <tr>
                        <td>التحقق من صحة البيانات</td>
                        <td class="old-design">أساسي</td>
                        <td class="new-design">متقدم مع رسائل مخصصة</td>
                        <td><span class="status-badge status-enhanced">معزز</span></td>
                    </tr>
                    <tr>
                        <td>تأثيرات الخلفية</td>
                        <td class="old-design">لا توجد</td>
                        <td class="new-design">تدرجات متحركة وblur</td>
                        <td><span class="status-badge status-new">جديد</span></td>
                    </tr>
                    <tr>
                        <td>أزرار التحكم</td>
                        <td class="old-design">لا توجد</td>
                        <td class="new-design">تبديل اللغة والثيم</td>
                        <td><span class="status-badge status-new">جديد</span></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h3>🧪 اختبار الوظائف</h3>
            <p>للاختبار الكامل، قم بما يلي:</p>
            <ol style="text-align: right; line-height: 2;">
                <li>افتح صفحة تسجيل الدخول الجديدة</li>
                <li>جرب تبديل اللغة باستخدام زر اللغة في الأعلى</li>
                <li>جرب تبديل الوضع الليلي/النهاري</li>
                <li>اختبر إظهار/إخفاء كلمة المرور</li>
                <li>جرب تسجيل الدخول بالبيانات: <EMAIL> / admin123</li>
                <li>اختبر رسائل الخطأ بإدخال بيانات خاطئة</li>
                <li>تحقق من التصميم المتجاوب على أجهزة مختلفة</li>
            </ol>
        </div>

        <div class="demo-section">
            <h2>🎯 النتيجة النهائية</h2>
            <p>تم إنشاء صفحة تسجيل دخول متطورة تتوافق مع معايير WebCore الذهبية</p>
            <div style="margin-top: 20px;">
                <span class="status-badge status-improved">✅ تصميم محسن</span>
                <span class="status-badge status-new">✅ دعم متعدد اللغات</span>
                <span class="status-badge status-enhanced">✅ تجربة مستخدم متقدمة</span>
            </div>
        </div>
    </div>
</body>
</html>
