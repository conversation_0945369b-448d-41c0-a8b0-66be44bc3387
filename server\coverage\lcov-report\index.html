
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.7% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>182/657</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.57% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>55/207</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.17% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>22/91</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.59% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>179/626</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="routes"><a href="routes/index.html">routes</a></td>
	<td data-value="37.31" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 37%"></div><div class="cover-empty" style="width: 63%"></div></div>
	</td>
	<td data-value="37.31" class="pct low">37.31%</td>
	<td data-value="268" class="abs low">100/268</td>
	<td data-value="20" class="pct low">20%</td>
	<td data-value="50" class="abs low">10/50</td>
	<td data-value="19.35" class="pct low">19.35%</td>
	<td data-value="31" class="abs low">6/31</td>
	<td data-value="37.45" class="pct low">37.45%</td>
	<td data-value="267" class="abs low">100/267</td>
	</tr>

<tr>
	<td class="file high" data-value="routes/middleware"><a href="routes/middleware/index.html">routes/middleware</a></td>
	<td data-value="85.71" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 85%"></div><div class="cover-empty" style="width: 15%"></div></div>
	</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="70" class="abs high">60/70</td>
	<td data-value="78.84" class="pct high">78.84%</td>
	<td data-value="52" class="abs high">41/52</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="86.36" class="pct high">86.36%</td>
	<td data-value="66" class="abs high">57/66</td>
	</tr>

<tr>
	<td class="file low" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="6.89" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.89" class="pct low">6.89%</td>
	<td data-value="319" class="abs low">22/319</td>
	<td data-value="3.8" class="pct low">3.8%</td>
	<td data-value="105" class="abs low">4/105</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="48" class="abs low">4/48</td>
	<td data-value="7.5" class="pct low">7.5%</td>
	<td data-value="293" class="abs low">22/293</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-27T00:24:48.023Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    