const request = require('supertest');
const app = require('../app');
const { setupTestDatabase } = require('./setup');

describe('Security Tests', () => {
  let testData;
  let adminToken;
  let userToken;

  beforeEach(async () => {
    testData = await setupTestDatabase();

    // Get tokens
    const adminLoginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword'
      });
    adminToken = adminLoginResponse.body.accessToken;

    const userLoginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword'
      });
    userToken = userLoginResponse.body.accessToken;
  });

  describe('Authentication Security', () => {
    test('should reject requests without token', async () => {
      const response = await request(app)
        .get('/api/roles');

      expect(response.status).toBe(401);
    });

    test('should reject requests with invalid token', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
    });

    test('should reject requests with malformed authorization header', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', 'InvalidFormat');

      expect(response.status).toBe(401);
    });
  });

  describe('Authorization Security', () => {
    test('should prevent privilege escalation - user cannot create roles', async () => {
      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${userToken}`)
        .send({
          name: 'malicious-role',
          description: 'Attempting privilege escalation',
          permissions: {
            users: ['create', 'read', 'update', 'delete'],
            roles: ['create', 'read', 'update', 'delete']
          }
        });

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });

    test('should prevent user from accessing admin-only endpoints', async () => {
      const endpoints = [
        { method: 'post', path: '/api/roles' },
        { method: 'put', path: '/api/roles/fake-id' },
        { method: 'delete', path: '/api/roles/fake-id' },
        { method: 'get', path: '/api/roles/permissions' }
      ];

      for (const endpoint of endpoints) {
        const response = await request(app)
          [endpoint.method](endpoint.path)
          .set('Authorization', `Bearer ${userToken}`);

        expect(response.status).toBe(403);
      }
    });

    test('should prevent user from accessing user management endpoints', async () => {
      const response = await request(app)
        .get('/api/users')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });
  });

  describe('Input Validation Security', () => {
    test('should validate role creation input', async () => {
      const maliciousInputs = [
        { name: '', permissions: {} }, // Empty name
        { name: 'test', permissions: null }, // Null permissions
        { name: 'test' }, // Missing permissions
        { permissions: { test: ['read'] } }, // Missing name
      ];

      for (const input of maliciousInputs) {
        const response = await request(app)
          .post('/api/roles')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(input);

        expect(response.status).toBe(400);
      }
    });

    test('should handle SQL injection attempts', async () => {
      const sqlInjectionAttempts = [
        "'; DROP TABLE roles; --",
        "' OR '1'='1",
        "'; DELETE FROM users; --"
      ];

      for (const maliciousName of sqlInjectionAttempts) {
        const response = await request(app)
          .post('/api/roles')
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            name: maliciousName,
            description: 'SQL Injection Test',
            permissions: { test: ['read'] }
          });

        // Should either succeed (if properly sanitized) or fail with validation error
        // But should not cause server error
        expect(response.status).not.toBe(500);
      }
    });
  });

  describe('Role Assignment Security', () => {
    test('should prevent role deletion when role is assigned to users', async () => {
      // Try to delete admin role (which is assigned to admin user)
      const response = await request(app)
        .delete(`/api/roles/${testData.adminRole.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Cannot delete role that is assigned to users');
    });

    test('should allow role deletion when role is not assigned', async () => {
      // Create a new role
      const createResponse = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'unassigned-role',
          description: 'Role not assigned to any user',
          permissions: { test: ['read'] }
        });

      const roleId = createResponse.body.id;

      // Delete the unassigned role
      const deleteResponse = await request(app)
        .delete(`/api/roles/${roleId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(deleteResponse.status).toBe(200);
      expect(deleteResponse.body.success).toBe(true);
    });
  });

  describe('Data Exposure Security', () => {
    test('should not expose sensitive user data in role responses', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      
      // Check that response doesn't contain sensitive data
      const responseString = JSON.stringify(response.body);
      expect(responseString).not.toContain('password');
      expect(responseString).not.toContain('$2b$'); // bcrypt hash prefix
    });

    test('should not expose internal system information in error messages', async () => {
      const response = await request(app)
        .get('/api/roles/non-existent-id')
        .set('Authorization', `Bearer ${adminToken}`);

      // Should not expose database schema or internal paths
      const responseString = JSON.stringify(response.body);
      expect(responseString).not.toContain('prisma');
      expect(responseString).not.toContain('database');
      expect(responseString).not.toContain('schema');
    });
  });
});
