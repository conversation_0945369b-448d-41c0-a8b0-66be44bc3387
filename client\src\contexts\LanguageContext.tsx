import React, { createContext, useContext, useState, useEffect } from 'react'

type Language = 'ar' | 'en'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string) => string
}

const translations = {
  ar: {
    'dashboard': 'لوحة التحكم',
    'users': 'إدارة المستخدمين',
    'roles': 'إدارة الأدوار',
    'modules': 'إدارة الموديولات',
    'audit': 'سجلات المراجعة',
    'integrations': 'التكاملات',
    'settings': 'الإعدادات',
    'search': 'البحث في النظام...',
    'logout': 'تسجيل الخروج',
    'welcome': 'مرحباً بك! إليك ما يحدث في نظامك.',
    'language': 'اللغة',
    'systemSettings': 'إعدادات النظام',
    'configureSystemPreferences': 'تكوين الإعدادات والتفضيلات على مستوى النظام',
    'saveChanges': 'حفظ التغييرات',
    'saving': 'جاري الحفظ...',
    'general': 'عام',
    'security': 'الأمان',
    'notifications': 'الإشعارات',
    'backup': 'النسخ الاحتياطي',
    'generalSettings': 'الإعدادات العامة',
    'configureBasicInfo': 'تكوين معلومات النظام الأساسية والمظهر',
    'companyLogo': 'شعار المؤسسة',
    // Login page translations
    'welcomeBack': 'مرحباً بعودتك',
    'loginDescription': 'أدخل بياناتك للمتابعة إلى النظام',
    'email': 'البريد الإلكتروني',
    'password': 'كلمة المرور',
    'emailPlaceholder': 'أدخل بريدك الإلكتروني',
    'passwordPlaceholder': 'أدخل كلمة المرور',
    'signIn': 'تسجيل الدخول',
    'loading': 'جاري التحميل...',
    'showPassword': 'إظهار كلمة المرور',
    'hidePassword': 'إخفاء كلمة المرور',
    'dontHaveAccount': 'ليس لديك حساب؟',
    'signUp': 'إنشاء حساب',
    'secureLogin': 'دخول آمن',
    'poweredBy': 'مدعوم بواسطة',
    'webCore': 'WebCore',
    'systemTitle': 'نظام إدارة المحتوى المتقدم',
    'loginSuccess': 'تم تسجيل الدخول بنجاح',
    'loginError': 'خطأ في تسجيل الدخول',
    'emailRequired': 'البريد الإلكتروني مطلوب',
    'passwordRequired': 'كلمة المرور مطلوبة',
    'invalidEmail': 'البريد الإلكتروني غير صحيح',
    // Toast notifications
    'success': 'نجح',
    'error': 'خطأ',
    'warning': 'تحذير',
    'info': 'معلومات',
    'loginSuccessTitle': 'تم تسجيل الدخول بنجاح',
    'loginSuccessDesc': 'مرحباً بك في النظام',
    'loginErrorTitle': 'فشل تسجيل الدخول',
    'loginErrorDesc': 'تحقق من بياناتك وحاول مرة أخرى',
    'invalidCredentials': 'بيانات الدخول غير صحيحة',
    'checkCredentials': 'تأكد من البريد الإلكتروني وكلمة المرور',
    'tryAgain': 'حاول مرة أخرى',
    'connectionError': 'خطأ في الاتصال',
    'serverError': 'خطأ في الخادم',
    'unexpectedError': 'حدث خطأ غير متوقع',
    'operationSuccessful': 'تمت العملية بنجاح',
    'operationFailed': 'فشلت العملية',
    'pleaseCheck': 'يرجى المراجعة',
    'additionalInfo': 'معلومات إضافية',
    'uploadLogo': 'رفع الشعار',
    'siteName': 'اسم الموقع',
    'timezone': 'المنطقة الزمنية',
    'siteDescription': 'وصف الموقع',
    'defaultLanguage': 'اللغة الافتراضية',
    'maintenanceMode': 'وضع الصيانة',
    'enableMaintenanceMode': 'تفعيل وضع الصيانة لتقييد الوصول',
    'securitySettings': 'إعدادات الأمان',
    'configureSecurityPolicies': 'تكوين سياسات الأمان ومتطلبات المصادقة',
    'minPasswordLength': 'الحد الأدنى لطول كلمة المرور',
    'maxLoginAttempts': 'الحد الأقصى لمحاولات تسجيل الدخول',
    'sessionTimeout': 'انتهاء صلاحية الجلسة (بالدقائق)',
    'requireSpecialChars': 'طلب أحرف خاصة',
    'passwordsRequireSpecial': 'يجب أن تحتوي كلمات المرور على أحرف خاصة',
    'twoFactorRequired': 'المصادقة الثنائية مطلوبة',
    'requireTwoFactor': 'طلب المصادقة الثنائية لجميع حسابات المستخدمين',
    'ipWhitelist': 'القائمة البيضاء لعناوين IP (واحد في كل سطر)',
    'notificationSettings': 'إعدادات الإشعارات',
    'configureNotificationChannels': 'تكوين قنوات الإشعارات وإعدادات SMTP',
    'emailNotifications': 'إشعارات البريد الإلكتروني',
    'enableEmailNotifications': 'تفعيل إشعارات البريد الإلكتروني لأحداث النظام',
    'smsNotifications': 'إشعارات الرسائل النصية',
    'enableSmsNotifications': 'تفعيل إشعارات الرسائل النصية للتنبيهات الحرجة',
    'pushNotifications': 'الإشعارات المنبثقة',
    'enablePushNotifications': 'تفعيل الإشعارات المنبثقة في المتصفح',
    'adminEmail': 'بريد المدير الإلكتروني',
    'smtpHost': 'خادم SMTP',
    'smtpPort': 'منفذ SMTP',
    'smtpUser': 'اسم مستخدم SMTP',
    'smtpPassword': 'كلمة مرور SMTP',
    'backupSettings': 'إعدادات النسخ الاحتياطي',
    'configureBackupPolicies': 'تكوين النسخ الاحتياطي التلقائي وسياسات الاحتفاظ بالبيانات',
    'autoBackup': 'النسخ الاحتياطي التلقائي',
    'enableAutoBackup': 'تفعيل النسخ الاحتياطي التلقائي المجدول',
    'backupFrequency': 'تكرار النسخ الاحتياطي',
    'retentionDays': 'فترة الاحتفاظ (بالأيام)',
    'backupLocation': 'موقع النسخ الاحتياطي',
    'createBackupNow': 'إنشاء نسخة احتياطية الآن',
    'restoreFromBackup': 'استعادة من النسخة الاحتياطية',
    'daily': 'يومي',
    'weekly': 'أسبوعي',
    'monthly': 'شهري',
    'arabic': 'العربية',
    'english': 'English',
    'spanish': 'Español',
    'french': 'Français',
    'riyadhTime': 'توقيت الرياض',
    'dubaiTime': 'توقيت دبي',
    'kuwaitTime': 'توقيت الكويت',
    'cairoTime': 'توقيت القاهرة',
    // Dashboard translations
    'totalUsers': 'إجمالي المستخدمين',
    'activeUsers': 'المستخدمون النشطون',
    'activeRoles': 'الأدوار النشطة',
    'systemHealth': 'صحة النظام',
    'recentActivity': 'النشاط الأخير',
    'quickActions': 'الإجراءات السريعة',
    'generateReport': 'إنشاء تقرير',
    'addNewUser': 'إضافة مستخدم جديد',
    'installModule': 'تثبيت موديول',
    'createRole': 'إنشاء دور',
    'viewAuditLogs': 'عرض سجلات المراجعة',
    'activeToday': 'نشط اليوم',
    'fromLastMonth': 'من الشهر الماضي',
    'activeModules': 'الموديولات النشطة',
    'latestSystemActivities': 'أحدث أنشطة النظام',
    'frequentlyUsedOperations': 'العمليات المستخدمة بكثرة',
    // User Management translations
    'userManagement': 'إدارة المستخدمين',
    'manageUserAccounts': 'إدارة حسابات المستخدمين والأدوار والصلاحيات',
    'import': 'استيراد',
    'export': 'تصدير',
    'addUser': 'إضافة مستخدم',
    'editUser': 'تعديل المستخدم',
    'updateUserInfo': 'تحديث معلومات المستخدم والصلاحيات',
    'createNewUserAccount': 'إنشاء حساب مستخدم جديد بالصلاحيات المناسبة',
    'manageAndMonitorUsers': 'إدارة ومراقبة حسابات المستخدمين في مؤسستك',
    'searchUsers': 'البحث عن المستخدمين...',
    'filterByRole': 'تصفية حسب الدور',
    'filterByStatus': 'تصفية حسب الحالة',
    'allRoles': 'جميع الأدوار',
    'allStatus': 'جميع الحالات',
    'admin': 'مدير',
    'manager': 'مدير قسم',
    'user': 'مستخدم',
    'active': 'نشط',
    'inactive': 'غير نشط',
    'suspended': 'معلق',
    'role': 'الدور',
    'department': 'القسم',
    'status': 'الحالة',
    'lastLogin': 'آخر تسجيل دخول',
    'actions': 'الإجراءات',
    'edit': 'تعديل',
    'suspend': 'تعليق',
    'activate': 'تفعيل',
    'delete': 'حذف',
    'fullName': 'الاسم الكامل',
    'email': 'البريد الإلكتروني',
    'cancel': 'إلغاء',
    'create': 'إنشاء',
    'update': 'تحديث',
    // Role Management translations
    'roleManagement': 'إدارة الأدوار',
    'configureRolesPermissions': 'تكوين الأدوار والصلاحيات مع التحكم الهرمي في الوصول',
    'editRole': 'تعديل الدور',
    'createNewRole': 'إنشاء دور جديد',
    'updateRoleInfo': 'تحديث معلومات الدور والصلاحيات',
    'defineNewRole': 'تعريف دور جديد بصلاحيات ومستويات وصول محددة',
    'totalRoles': 'إجمالي الأدوار',
    'systemRoles': 'أدوار النظام',
    'customRoles': 'الأدوار المخصصة',
    'manageRoleHierarchy': 'إدارة التسلسل الهرمي للأدوار وتعيين الصلاحيات',
    'searchRoles': 'البحث عن الأدوار...',
    'roleName': 'اسم الدور',
    'description': 'الوصف',
    'type': 'النوع',
    'created': 'تاريخ الإنشاء',
    'childRole': 'دور فرعي',
    'system': 'نظام',
    'custom': 'مخصص',
    'viewUsers': 'عرض المستخدمين',
    'permissions': 'الصلاحيات',
    // Module Management translations
    'moduleManagement': 'إدارة الموديولات',
    'installConfigureMonitor': 'تثبيت وتكوين ومراقبة موديولات النظام',
    'browseLibrary': 'تصفح المكتبة',
    'uploadModule': 'رفع موديول',
    'uploadNewModule': 'رفع موديول جديد',
    'uploadModulePackage': 'رفع حزمة موديول (.zip) لتثبيت وظائف جديدة',
    'totalModules': 'إجمالي الموديولات',
    'issues': 'المشاكل',
    'avgHealth': 'متوسط الصحة',
    'installedModules': 'الموديولات المثبتة',
    'manageAndMonitorModules': 'إدارة ومراقبة موديولات النظام المثبتة',
    'searchModules': 'البحث عن الموديولات...',
    'allCategories': 'جميع الفئات',
    'authentication': 'المصادقة',
    'integration': 'التكامل',
    'analytics': 'التحليلات',
    'version': 'الإصدار',
    'health': 'الصحة',
    'author': 'المؤلف',
    'size': 'الحجم',
    'installed': 'تاريخ التثبيت',
    'configure': 'تكوين',
    'viewLogs': 'عرض السجلات',
    'uninstall': 'إلغاء التثبيت',
    'modulePackage': 'حزمة الموديول (.zip)',
    'uploadAndInstall': 'رفع وتثبيت',
    'uploading': 'جاري الرفع...',
    // Audit Logs translations
    'auditLogs': 'سجلات المراجعة',
    'monitorSystemActivities': 'مراقبة أنشطة النظام وأحداث الأمان مع مسارات المراجعة غير القابلة للتغيير',
    'exportLogs': 'تصدير السجلات',
    'totalEvents': 'إجمالي الأحداث',
    'successful': 'ناجح',
    'failed': 'فاشل',
    'criticalEvents': 'الأحداث الحرجة',
    'auditTrail': 'مسار المراجعة',
    'comprehensiveLog': 'سجل شامل لجميع أنشطة النظام وأحداث الأمان',
    'searchLogs': 'البحث في السجلات...',
    'allSeverities': 'جميع مستويات الخطورة',
    'critical': 'حرج',
    'high': 'عالي',
    'medium': 'متوسط',
    'low': 'منخفض',
    'timestamp': 'الطابع الزمني',
    'action': 'الإجراء',
    'resource': 'المورد',
    'severity': 'الخطورة',
    'details': 'التفاصيل',
    'success': 'نجح',
    // Integrations translations
    'connectExternalServices': 'الاتصال بالخدمات الخارجية وإدارة تكاملات API',
    'addIntegration': 'إضافة تكامل',
    'totalIntegrations': 'إجمالي التكاملات',
    'connected': 'متصل',
    'errors': 'أخطاء',
    'categories': 'الفئات',
    'allIntegrations': 'جميع التكاملات',
    'calendar': 'التقويم',
    'communication': 'التواصل',
    'webhooks': 'الخطافات الويب',
    'test': 'اختبار',
    'lastSync': 'آخر مزامنة',
    'disconnected': 'منقطع',
    'error': 'خطأ',
    'calendarIntegration': 'تكامل التقويم',
    'videoConferencing': 'مؤتمرات الفيديو',
    'openCalendar': 'فتح التقويم',
    'startMeeting': 'بدء اجتماع',
    'webhookEndpoints': 'نقاط نهاية الخطافات الويب',
    'configureWebhookEndpoints': 'تكوين نقاط نهاية الخطافات الويب للإشعارات الفورية للأحداث',
    'apiKey': 'مفتاح API',
    'endpointUrl': 'رابط نقطة النهاية',
    'enterApiKey': 'أدخل مفتاح API',
    'saveConfiguration': 'حفظ التكوين'
  },
  en: {
    'dashboard': 'Dashboard',
    'users': 'User Management',
    'roles': 'Role Management',
    'modules': 'Module Management',
    'audit': 'Audit Logs',
    'integrations': 'Integrations',
    'settings': 'Settings',
    'search': 'Search system...',
    'logout': 'Logout',
    'welcome': 'Welcome back! Here\'s what\'s happening with your system.',
    'language': 'Language',
    'systemSettings': 'System Settings',
    'configureSystemPreferences': 'Configure system-wide settings and preferences',
    'saveChanges': 'Save Changes',
    'saving': 'Saving...',
    'general': 'General',
    'security': 'Security',
    'notifications': 'Notifications',
    'backup': 'Backup',
    'generalSettings': 'General Settings',
    'configureBasicInfo': 'Configure basic system information and appearance',
    'companyLogo': 'Company Logo',
    // Login page translations
    'welcomeBack': 'Welcome Back',
    'loginDescription': 'Enter your credentials to continue to the system',
    'email': 'Email',
    'password': 'Password',
    'emailPlaceholder': 'Enter your email',
    'passwordPlaceholder': 'Enter your password',
    'signIn': 'Sign In',
    'loading': 'Loading...',
    'showPassword': 'Show password',
    'hidePassword': 'Hide password',
    'dontHaveAccount': 'Don\'t have an account?',
    'signUp': 'Sign up',
    'secureLogin': 'Secure Login',
    'poweredBy': 'Powered by',
    'webCore': 'WebCore',
    'systemTitle': 'Advanced Content Management System',
    'loginSuccess': 'Logged in successfully',
    'loginError': 'Login error',
    'emailRequired': 'Email is required',
    'passwordRequired': 'Password is required',
    'invalidEmail': 'Invalid email format',
    // Toast notifications
    'success': 'Success',
    'error': 'Error',
    'warning': 'Warning',
    'info': 'Information',
    'loginSuccessTitle': 'Login Successful',
    'loginSuccessDesc': 'Welcome to the system',
    'loginErrorTitle': 'Login Failed',
    'loginErrorDesc': 'Please check your credentials and try again',
    'invalidCredentials': 'Invalid credentials',
    'checkCredentials': 'Please check your email and password',
    'tryAgain': 'Please try again',
    'connectionError': 'Connection error',
    'serverError': 'Server error',
    'unexpectedError': 'An unexpected error occurred',
    'operationSuccessful': 'Operation completed successfully',
    'operationFailed': 'Operation failed',
    'pleaseCheck': 'Please check',
    'additionalInfo': 'Additional information',
    'uploadLogo': 'Upload Logo',
    'siteName': 'Site Name',
    'timezone': 'Timezone',
    'siteDescription': 'Site Description',
    'defaultLanguage': 'Default Language',
    'maintenanceMode': 'Maintenance Mode',
    'enableMaintenanceMode': 'Enable maintenance mode to restrict access',
    'securitySettings': 'Security Settings',
    'configureSecurityPolicies': 'Configure security policies and authentication requirements',
    'minPasswordLength': 'Minimum Password Length',
    'maxLoginAttempts': 'Maximum Login Attempts',
    'sessionTimeout': 'Session Timeout (minutes)',
    'requireSpecialChars': 'Require Special Characters',
    'passwordsRequireSpecial': 'Passwords must contain special characters',
    'twoFactorRequired': 'Two-Factor Authentication Required',
    'requireTwoFactor': 'Require two-factor authentication for all user accounts',
    'ipWhitelist': 'IP Whitelist (one per line)',
    'notificationSettings': 'Notification Settings',
    'configureNotificationChannels': 'Configure notification channels and SMTP settings',
    'emailNotifications': 'Email Notifications',
    'enableEmailNotifications': 'Enable email notifications for system events',
    'smsNotifications': 'SMS Notifications',
    'enableSmsNotifications': 'Enable SMS notifications for critical alerts',
    'pushNotifications': 'Push Notifications',
    'enablePushNotifications': 'Enable browser push notifications',
    'adminEmail': 'Admin Email',
    'smtpHost': 'SMTP Host',
    'smtpPort': 'SMTP Port',
    'smtpUser': 'SMTP Username',
    'smtpPassword': 'SMTP Password',
    'backupSettings': 'Backup Settings',
    'configureBackupPolicies': 'Configure automatic backup and data retention policies',
    'autoBackup': 'Automatic Backup',
    'enableAutoBackup': 'Enable scheduled automatic backups',
    'backupFrequency': 'Backup Frequency',
    'retentionDays': 'Retention Period (days)',
    'backupLocation': 'Backup Location',
    'createBackupNow': 'Create Backup Now',
    'restoreFromBackup': 'Restore from Backup',
    'daily': 'Daily',
    'weekly': 'Weekly',
    'monthly': 'Monthly',
    'arabic': 'العربية',
    'english': 'English',
    'spanish': 'Español',
    'french': 'Français',
    'riyadhTime': 'Riyadh Time',
    'dubaiTime': 'Dubai Time',
    'kuwaitTime': 'Kuwait Time',
    'cairoTime': 'Cairo Time',
    // Dashboard translations
    'totalUsers': 'Total Users',
    'activeUsers': 'Active Users',
    'activeRoles': 'Active Roles',
    'systemHealth': 'System Health',
    'recentActivity': 'Recent Activity',
    'quickActions': 'Quick Actions',
    'generateReport': 'Generate Report',
    'addNewUser': 'Add New User',
    'installModule': 'Install Module',
    'createRole': 'Create Role',
    'viewAuditLogs': 'View Audit Logs',
    'activeToday': 'active today',
    'fromLastMonth': 'from last month',
    'activeModules': 'Active modules',
    'latestSystemActivities': 'Latest system activities and user actions',
    'frequentlyUsedOperations': 'Frequently used system operations',
    // User Management translations
    'userManagement': 'User Management',
    'manageUserAccounts': 'Manage user accounts, roles, and permissions',
    'import': 'Import',
    'export': 'Export',
    'addUser': 'Add User',
    'editUser': 'Edit User',
    'updateUserInfo': 'Update user information and permissions',
    'createNewUserAccount': 'Create a new user account with appropriate permissions',
    'manageAndMonitorUsers': 'Manage and monitor user accounts across your organization',
    'searchUsers': 'Search users...',
    'filterByRole': 'Filter by role',
    'filterByStatus': 'Filter by status',
    'allRoles': 'All Roles',
    'allStatus': 'All Status',
    'admin': 'Admin',
    'manager': 'Manager',
    'user': 'User',
    'active': 'Active',
    'inactive': 'Inactive',
    'suspended': 'Suspended',
    'role': 'Role',
    'department': 'Department',
    'status': 'Status',
    'lastLogin': 'Last Login',
    'actions': 'Actions',
    'edit': 'Edit',
    'suspend': 'Suspend',
    'activate': 'Activate',
    'delete': 'Delete',
    'fullName': 'Full Name',
    'email': 'Email',
    'cancel': 'Cancel',
    'create': 'Create',
    'update': 'Update',
    // Role Management translations
    'roleManagement': 'Role Management',
    'configureRolesPermissions': 'Configure roles and permissions with hierarchical access control',
    'editRole': 'Edit Role',
    'createNewRole': 'Create New Role',
    'updateRoleInfo': 'Update role information and permissions',
    'defineNewRole': 'Define a new role with specific permissions and access levels',
    'totalRoles': 'Total Roles',
    'systemRoles': 'System Roles',
    'customRoles': 'Custom Roles',
    'manageRoleHierarchy': 'Manage role hierarchy and permission assignments',
    'searchRoles': 'Search roles...',
    'roleName': 'Role Name',
    'description': 'Description',
    'type': 'Type',
    'created': 'Created',
    'childRole': 'Child Role',
    'system': 'System',
    'custom': 'Custom',
    'viewUsers': 'View Users',
    'permissions': 'Permissions',
    // Module Management translations
    'moduleManagement': 'Module Management',
    'installConfigureMonitor': 'Install, configure, and monitor system modules',
    'browseLibrary': 'Browse Library',
    'uploadModule': 'Upload Module',
    'uploadNewModule': 'Upload New Module',
    'uploadModulePackage': 'Upload a module package (.zip file) to install new functionality',
    'totalModules': 'Total Modules',
    'issues': 'Issues',
    'avgHealth': 'Avg Health',
    'installedModules': 'Installed Modules',
    'manageAndMonitorModules': 'Manage and monitor your installed system modules',
    'searchModules': 'Search modules...',
    'allCategories': 'All Categories',
    'authentication': 'Authentication',
    'integration': 'Integration',
    'analytics': 'Analytics',
    'version': 'Version',
    'health': 'Health',
    'author': 'Author',
    'size': 'Size',
    'installed': 'Installed',
    'configure': 'Configure',
    'viewLogs': 'View Logs',
    'uninstall': 'Uninstall',
    'modulePackage': 'Module Package (.zip)',
    'uploadAndInstall': 'Upload & Install',
    'uploading': 'Uploading...',
    // Audit Logs translations
    'auditLogs': 'Audit Logs',
    'monitorSystemActivities': 'Monitor system activities and security events with immutable audit trails',
    'exportLogs': 'Export Logs',
    'totalEvents': 'Total Events',
    'successful': 'Successful',
    'failed': 'Failed',
    'criticalEvents': 'Critical Events',
    'auditTrail': 'Audit Trail',
    'comprehensiveLog': 'Comprehensive log of all system activities and security events',
    'searchLogs': 'Search logs...',
    'allSeverities': 'All Severities',
    'critical': 'Critical',
    'high': 'High',
    'medium': 'Medium',
    'low': 'Low',
    'timestamp': 'Timestamp',
    'action': 'Action',
    'resource': 'Resource',
    'severity': 'Severity',
    'details': 'Details',
    'success': 'Success',
    // Integrations translations
    'connectExternalServices': 'Connect with external services and manage API integrations',
    'addIntegration': 'Add Integration',
    'totalIntegrations': 'Total Integrations',
    'connected': 'Connected',
    'errors': 'Errors',
    'categories': 'Categories',
    'allIntegrations': 'All Integrations',
    'calendar': 'Calendar',
    'communication': 'Communication',
    'webhooks': 'Webhooks',
    'test': 'Test',
    'lastSync': 'Last sync',
    'disconnected': 'Disconnected',
    'error': 'Error',
    'calendarIntegration': 'Calendar Integration',
    'videoConferencing': 'Video Conferencing',
    'openCalendar': 'Open Calendar',
    'startMeeting': 'Start Meeting',
    'webhookEndpoints': 'Webhook Endpoints',
    'configureWebhookEndpoints': 'Configure webhook endpoints for real-time event notifications',
    'apiKey': 'API Key',
    'endpointUrl': 'Endpoint URL',
    'enterApiKey': 'Enter API key',
    'saveConfiguration': 'Save Configuration'
  }
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>(() => {
    const saved = localStorage.getItem('language')
    return (saved as Language) || 'en'
  })

  useEffect(() => {
    localStorage.setItem('language', language)
    document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = language
  }, [language])

  const t = (key: string): string => {
    return translations[language][key] || key
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}