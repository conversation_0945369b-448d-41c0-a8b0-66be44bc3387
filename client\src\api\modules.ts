import api from './api';

// Description: Get list of all installed modules
// Endpoint: GET /api/modules
// Request: {}
// Response: { modules: Array<{_id: string, name: string, version: string, description: string, status: string, health: number, category: string, author: string, size: string, installedAt: string, lastUpdate: string, dependencies: string[]}> }
export const getModules = async (filters?: { category?: string; status?: string; search?: string }) => {
  try {
    const params = new URLSearchParams();
    if (filters?.category) params.append('category', filters.category);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.search) params.append('search', filters.search);
    
    const queryString = params.toString();
    const url = queryString ? `/api/modules?${queryString}` : '/api/modules';
    
    const response = await api.get(url);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching modules:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Failed to fetch modules');
  }
}

// Description: Upload and install a new module
// Endpoint: POST /api/modules/upload
// Request: FormData with module file
// Response: { success: boolean, message: string, module: {_id: string, name: string, version: string} }
export const uploadModule = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('module', file);
    
    const response = await api.post('/api/modules/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    
    return response.data;
  } catch (error: any) {
    console.error('Error uploading module:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Failed to upload module');
  }
}

// Description: Toggle module active/inactive status
// Endpoint: PUT /api/modules/:id/toggle
// Request: { enabled: boolean }
// Response: { success: boolean, message: string }
export const toggleModule = async (moduleId: string, enabled: boolean) => {
  try {
    const response = await api.put(`/api/modules/${moduleId}/toggle`, { enabled });
    return response.data;
  } catch (error: any) {
    console.error('Error toggling module:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Failed to toggle module');
  }
}

// Description: Delete/uninstall a module
// Endpoint: DELETE /api/modules/:id
// Request: {}
// Response: { success: boolean, message: string }
export const deleteModule = async (moduleId: string) => {
  try {
    const response = await api.delete(`/api/modules/${moduleId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting module:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Failed to delete module');
  }
}

// Description: Get modules that should appear in navigation
// Endpoint: GET /api/modules/navigation
// Request: {}
// Response: { modules: Array<{_id: string, name: string, icon: string, path: string, order: number}> }
export const getNavigationModules = async () => {
  try {
    const response = await api.get('/api/modules/navigation');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching navigation modules:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Failed to fetch navigation modules');
  }
}

// Description: Get module health and performance metrics
// Endpoint: GET /api/modules/:id/health
// Request: {}
// Response: { health: number, metrics: {cpu: number, memory: number, responseTime: number}, errors: string[] }
export const getModuleHealth = async (moduleId: string) => {
  try {
    const response = await api.get(`/api/modules/${moduleId}/health`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching module health:', error);
    throw new Error(error?.response?.data?.message || error.message || 'Failed to fetch module health');
  }
}