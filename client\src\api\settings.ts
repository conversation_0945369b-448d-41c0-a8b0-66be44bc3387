import api from './api';

export interface SystemSettings {
  general: {
    siteName: string;
    siteDescription: string;
    logo: string;
    timezone: string;
    language: string;
    maintenanceMode: boolean;
  };
  security: {
    passwordMinLength: number;
    passwordRequireSpecial: boolean;
    sessionTimeout: number;
    maxLoginAttempts: number;
    twoFactorRequired: boolean;
    ipWhitelist: string[];
  };
  notifications: {
    emailEnabled: boolean;
    smsEnabled: boolean;
    pushEnabled: boolean;
    adminEmail: string;
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
  };
  backup: {
    autoBackup: boolean;
    backupFrequency: string;
    retentionDays: number;
    backupLocation: string;
  };
}

export interface Timezone {
  value: string;
  label: string;
  offset: string;
}

// Get all system settings (requires authentication)
export const getSystemSettings = async (): Promise<SystemSettings> => {
  try {
    const response = await api.get('/api/settings');
    return response.data.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Get public system settings (no authentication required)
export const getPublicSettings = async (): Promise<Partial<SystemSettings>> => {
  try {
    const response = await api.get('/api/settings/public');
    return response.data.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Update system settings
export const updateSystemSettings = async (settings: Partial<SystemSettings>): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.put('/api/settings', settings);
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Get settings by category
export const getSettingsByCategory = async (category: string): Promise<any> => {
  try {
    const response = await api.get(`/api/settings/category/${category}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Get available timezones
export const getTimezones = async (): Promise<Timezone[]> => {
  try {
    const response = await api.get('/api/settings/timezones');
    return response.data.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Get specific setting value
export const getSetting = async (category: string, key: string): Promise<any> => {
  try {
    const response = await api.get(`/api/settings/${category}/${key}`);
    return response.data.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Set specific setting value
export const setSetting = async (category: string, key: string, value: any, description?: string): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.put(`/api/settings/${category}/${key}`, { value, description });
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Initialize default settings
export const initializeDefaultSettings = async (): Promise<{ success: boolean; message: string }> => {
  try {
    const response = await api.post('/api/settings/initialize');
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || error.message);
  }
};

// Description: Get email template preview
// Endpoint: POST /api/settings/email-templates/preview
// Request: { templateType: string, subject: string, body: string, sampleData: object }
// Response: { success: boolean, preview: {subject: string, body: string} }
export const previewEmailTemplate = (templateData: any) => {
  // Mocking the response
  return new Promise((resolve) => {
    setTimeout(() => {
      const sampleData = {
        name: 'John Doe',
        resetLink: 'https://webvue.com/reset-password?token=abc123',
        alertType: 'Security Alert',
        message: 'Multiple failed login attempts detected',
        timestamp: new Date().toLocaleString()
      };
      
      let previewSubject = templateData.subject;
      let previewBody = templateData.body;
      
      // Replace placeholders with sample data
      Object.keys(sampleData).forEach(key => {
        const placeholder = `{{${key}}}`;
        const value = sampleData[key as keyof typeof sampleData];
        previewSubject = previewSubject.replace(new RegExp(placeholder, 'g'), value);
        previewBody = previewBody.replace(new RegExp(placeholder, 'g'), value);
      });
      
      resolve({
        success: true,
        preview: {
          subject: previewSubject,
          body: previewBody
        }
      });
    }, 500);
  });
  // Uncomment the below lines to make an actual API call
  // try {
  //   return await api.post('/api/settings/email-templates/preview', templateData);
  // } catch (error) {
  //   throw new Error(error?.response?.data?.message || error.message);
  // }
}