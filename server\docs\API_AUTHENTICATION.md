# 🔌 دليل API للمصادقة - WebCore

## نظرة عامة

هذا الدليل يوضح جميع نقاط النهاية (endpoints) المتعلقة بنظام المصادقة والأمان في WebCore.

## 🔐 Authentication Endpoints

### 1. تسجيل الدخول

**POST** `/api/auth/login`

تسجيل دخول المستخدم والحصول على توكنات الوصول.

#### Request Body
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "cmdkyuh2p0002nh7owoxgqsd3",
      "email": "<EMAIL>",
      "isActive": true,
      "roles": [
        {
          "role": {
            "id": "role-id",
            "name": "user",
            "permissions": ["read:profile"]
          }
        }
      ]
    }
  }
}
```

#### Error Responses
```json
// 400 Bad Request - بيانات غير صحيحة
{
  "success": false,
  "error": "Email or password is incorrect"
}

// 429 Too Many Requests - تجاوز حد المحاولات
{
  "error": "Too many login attempts, please try again later.",
  "retryAfter": "15 minutes"
}
```

#### Rate Limiting
- **الحد الأقصى**: 5 محاولات كل 15 دقيقة لكل IP
- **التتبع**: حسب IP + البريد الإلكتروني

---

### 2. تجديد التوكن

**POST** `/api/auth/refresh`

تجديد توكن الوصول باستخدام توكن التجديد.

#### Request Body
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user-id",
      "email": "<EMAIL>"
    }
  }
}
```

#### Error Responses
```json
// 401 Unauthorized - توكن غير صالح
{
  "success": false,
  "error": "Invalid or expired refresh token"
}
```

---

### 3. الحصول على بيانات المستخدم الحالي

**GET** `/api/auth/me`

الحصول على بيانات المستخدم المصادق عليه حالياً.

#### Headers
```
Authorization: Bearer <access-token>
```

#### Response (200 OK)
```json
{
  "id": "cmdkyuh2p0002nh7owoxgqsd3",
  "email": "<EMAIL>",
  "isActive": true,
  "createdAt": "2025-07-27T00:00:00.000Z",
  "lastLoginAt": "2025-07-27T00:30:00.000Z",
  "roles": [
    {
      "role": {
        "id": "role-id",
        "name": "user",
        "permissions": ["read:profile", "update:profile"],
        "isActive": true
      }
    }
  ]
}
```

#### Error Responses
```json
// 401 Unauthorized - توكن مفقود أو غير صالح
{
  "error": "Invalid or expired token"
}
```

---

### 4. تسجيل الخروج

**POST** `/api/auth/logout`

تسجيل خروج المستخدم وإلغاء التوكن.

#### Headers
```
Authorization: Bearer <access-token>
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

#### Error Responses
```json
// 401 Unauthorized - توكن غير صالح
{
  "error": "Invalid token"
}
```

---

## 📊 Metrics Endpoints

### 1. فحص صحة النظام

**GET** `/api/metrics/health`

فحص حالة النظام والخدمات.

#### Response (200 OK)
```json
{
  "status": "healthy",
  "timestamp": "2025-07-27T00:56:50.082Z",
  "uptime": 3600,
  "memory": {
    "heapUsed": 25,
    "heapTotal": 39,
    "usage": 64
  },
  "database": {
    "status": "healthy",
    "responseTime": 15
  },
  "requests": {
    "total": 150,
    "averageResponseTime": 21,
    "errorRate": 0
  }
}
```

#### Status Values
- `healthy`: النظام يعمل بشكل طبيعي
- `degraded`: النظام يعمل ولكن بأداء منخفض
- `unhealthy`: النظام يواجه مشاكل

---

### 2. مقاييس الأداء

**GET** `/api/metrics/performance`

الحصول على مقاييس أداء النظام التفصيلية.

#### Headers
```
Authorization: Bearer <admin-token>
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "requests": {
      "total": 1250,
      "successful": 1248,
      "failed": 2,
      "averageResponseTime": 45.2,
      "slowQueries": 3
    },
    "authentication": {
      "logins": 85,
      "loginFailures": 2,
      "tokenVerifications": 1165,
      "averageLoginTime": 265,
      "averageVerificationTime": 20
    },
    "memory": {
      "heapUsed": 25,
      "heapTotal": 39,
      "external": 8,
      "rss": 45
    },
    "errors": {
      "total": 2,
      "authentication": 2,
      "database": 0,
      "validation": 0,
      "server": 0
    },
    "uptime": 7200,
    "timestamp": "2025-07-27T00:56:50.082Z"
  }
}
```

---

### 3. مقاييس المصادقة

**GET** `/api/metrics/auth`

مقاييس خاصة بنظام المصادقة.

#### Headers
```
Authorization: Bearer <admin-token>
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "authentication": {
      "logins": 85,
      "loginFailures": 2,
      "tokenVerifications": 1165,
      "averageLoginTime": 265,
      "averageVerificationTime": 20
    },
    "errors": {
      "authentication": 2,
      "total": 2
    },
    "performance": {
      "averageLoginTime": 265,
      "averageVerificationTime": 20
    },
    "rates": {
      "loginSuccessRate": 98,
      "loginFailureRate": 2
    },
    "timestamp": "2025-07-27T00:56:50.082Z"
  }
}
```

---

### 4. إعادة تعيين المقاييس

**POST** `/api/metrics/reset`

إعادة تعيين جميع مقاييس الأداء.

#### Headers
```
Authorization: Bearer <admin-token>
```

#### Response (200 OK)
```json
{
  "success": true,
  "message": "Performance metrics reset successfully",
  "timestamp": "2025-07-27T00:56:50.082Z"
}
```

---

## 🔒 Security Headers

جميع الاستجابات تتضمن رؤوس الأمان التالية:

```
Content-Security-Policy: default-src 'self'; frame-src 'none'; object-src 'none'
Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
X-Response-Time: 25ms
X-Request-ID: req-12345
```

## 📝 Error Codes

| Code | Description | Arabic |
|------|-------------|---------|
| 400 | Bad Request | طلب غير صحيح |
| 401 | Unauthorized | غير مصرح |
| 403 | Forbidden | محظور |
| 429 | Too Many Requests | طلبات كثيرة جداً |
| 500 | Internal Server Error | خطأ في الخادم |

## 🔧 Rate Limiting

### Login Endpoint
- **الحد**: 5 طلبات كل 15 دقيقة
- **المفتاح**: IP + البريد الإلكتروني
- **الاستجابة**: 429 مع `Retry-After` header

### API Endpoints
- **الحد**: 100 طلب كل 15 دقيقة
- **المفتاح**: IP Address
- **التباطؤ**: تدريجي بعد 50 طلب

## 📋 أمثلة الاستخدام

### JavaScript/Fetch
```javascript
// تسجيل الدخول
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const { data } = await loginResponse.json();
const accessToken = data.accessToken;

// استخدام التوكن
const userResponse = await fetch('/api/auth/me', {
  headers: {
    'Authorization': `Bearer ${accessToken}`
  }
});
```

### cURL
```bash
# تسجيل الدخول
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# الحصول على بيانات المستخدم
curl -X GET http://localhost:3000/api/auth/me \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

---

**تاريخ آخر تحديث**: 27 يوليو 2025  
**الإصدار**: 2.0.0  
**حالة التوثيق**: ✅ محدث ومكتمل
