
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for routes</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> routes</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.31% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>100/268</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>10/50</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.35% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/31</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">37.45% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>100/267</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="authRoutes.js"><a href="authRoutes.js.html">authRoutes.js</a></td>
	<td data-value="38.18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 38%"></div><div class="cover-empty" style="width: 62%"></div></div>
	</td>
	<td data-value="38.18" class="pct low">38.18%</td>
	<td data-value="55" class="abs low">21/55</td>
	<td data-value="22.22" class="pct low">22.22%</td>
	<td data-value="18" class="abs low">4/18</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="6" class="abs low">1/6</td>
	<td data-value="38.88" class="pct low">38.88%</td>
	<td data-value="54" class="abs low">21/54</td>
	</tr>

<tr>
	<td class="file low" data-value="dashboardRoutes.js"><a href="dashboardRoutes.js.html">dashboardRoutes.js</a></td>
	<td data-value="28" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28" class="pct low">28%</td>
	<td data-value="25" class="abs low">7/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="28" class="pct low">28%</td>
	<td data-value="25" class="abs low">7/25</td>
	</tr>

<tr>
	<td class="file high" data-value="index.js"><a href="index.js.html">index.js</a></td>
	<td data-value="71.42" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.42" class="pct high">71.42%</td>
	<td data-value="7" class="abs high">5/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="71.42" class="pct high">71.42%</td>
	<td data-value="7" class="abs high">5/7</td>
	</tr>

<tr>
	<td class="file low" data-value="moduleRoutes.js"><a href="moduleRoutes.js.html">moduleRoutes.js</a></td>
	<td data-value="43.75" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 43%"></div><div class="cover-empty" style="width: 57%"></div></div>
	</td>
	<td data-value="43.75" class="pct low">43.75%</td>
	<td data-value="16" class="abs low">7/16</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="43.75" class="pct low">43.75%</td>
	<td data-value="16" class="abs low">7/16</td>
	</tr>

<tr>
	<td class="file high" data-value="roleRoutes.js"><a href="roleRoutes.js.html">roleRoutes.js</a></td>
	<td data-value="68.62" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 68%"></div><div class="cover-empty" style="width: 32%"></div></div>
	</td>
	<td data-value="68.62" class="pct high">68.62%</td>
	<td data-value="51" class="abs high">35/51</td>
	<td data-value="50" class="pct high">50%</td>
	<td data-value="12" class="abs high">6/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="5" class="abs high">5/5</td>
	<td data-value="68.62" class="pct high">68.62%</td>
	<td data-value="51" class="abs high">35/51</td>
	</tr>

<tr>
	<td class="file low" data-value="settingsRoutes.js"><a href="settingsRoutes.js.html">settingsRoutes.js</a></td>
	<td data-value="21.53" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 21%"></div><div class="cover-empty" style="width: 79%"></div></div>
	</td>
	<td data-value="21.53" class="pct low">21.53%</td>
	<td data-value="65" class="abs low">14/65</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="21.53" class="pct low">21.53%</td>
	<td data-value="65" class="abs low">14/65</td>
	</tr>

<tr>
	<td class="file low" data-value="userRoutes.js"><a href="userRoutes.js.html">userRoutes.js</a></td>
	<td data-value="22.44" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22.44" class="pct low">22.44%</td>
	<td data-value="49" class="abs low">11/49</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="5" class="abs low">0/5</td>
	<td data-value="22.44" class="pct low">22.44%</td>
	<td data-value="49" class="abs low">11/49</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-27T00:24:48.023Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    