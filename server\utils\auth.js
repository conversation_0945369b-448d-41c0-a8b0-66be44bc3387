const jwt = require('jsonwebtoken');

const generateAccessToken = (user) => {
  const payload = {
    sub: user.id,  // Fixed: use user.id instead of user._id for Prisma
    iat: Math.floor(Date.now() / 1000),
    type: 'access'
  };
  return jwt.sign(payload, process.env.JWT_SECRET, { expiresIn: '15m' }); // تقليل من 1 يوم إلى 15 دقيقة
};

const generateRefreshToken = (user) => {
  const payload = {
    sub: user.id,  // Fixed: use user.id instead of user._id for Prisma
    iat: Math.floor(Date.now() / 1000),
    type: 'refresh'
  };
  return jwt.sign(payload, process.env.REFRESH_TOKEN_SECRET, { expiresIn: '7d' }); // تقليل من 30 يوم إلى 7 أيام
};

module.exports = {
  generateAccessToken,
  generateRefreshToken
};
