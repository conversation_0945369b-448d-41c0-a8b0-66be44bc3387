const express = require('express');
const { prisma } = require('../config/prisma.js');
const { requireUser } = require('./middleware/auth.js');

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Dashboard
 *   description: Dashboard statistics and data endpoints
 */

/**
 * @swagger
 * /api/dashboard/stats:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dashboard statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalUsers:
 *                   type: number
 *                   description: Total number of users
 *                 activeUsers:
 *                   type: number
 *                   description: Number of active users
 *                 totalRoles:
 *                   type: number
 *                   description: Total number of roles
 *                 totalModules:
 *                   type: number
 *                   description: Total number of modules
 *                 recentLogins:
 *                   type: number
 *                   description: Recent logins count
 *                 systemHealth:
 *                   type: object
 *                   properties:
 *                     database:
 *                       type: string
 *                       enum: [healthy, warning, error]
 *                     server:
 *                       type: string
 *                       enum: [healthy, warning, error]
 *             example:
 *               totalUsers: 25
 *               activeUsers: 20
 *               totalRoles: 5
 *               totalModules: 8
 *               recentLogins: 12
 *               systemHealth:
 *                 database: "healthy"
 *                 server: "healthy"
 */
router.get('/stats', requireUser, async (req, res) => {
  try {
    // Get total users count
    const totalUsers = await prisma.user.count();
    
    // Get active users count
    const activeUsers = await prisma.user.count({
      where: { isActive: true }
    });
    
    // Get total roles count
    const totalRoles = await prisma.role.count();
    
    // Get total modules count
    const totalModules = await prisma.module.count();
    
    // Get recent logins count (last 24 hours)
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    
    const recentLogins = await prisma.user.count({
      where: {
        lastLoginAt: {
          gte: yesterday
        }
      }
    });
    
    // System health check
    const systemHealth = {
      database: 'healthy',
      server: 'healthy'
    };
    
    res.json({
      totalUsers,
      activeUsers,
      totalRoles,
      totalModules,
      recentLogins,
      systemHealth
    });
    
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ error: 'Failed to fetch dashboard statistics' });
  }
});

/**
 * @swagger
 * /api/dashboard/recent-activity:
 *   get:
 *     summary: Get recent system activity
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of recent activities to return
 *     responses:
 *       200:
 *         description: Recent activities retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/AuditLog'
 */
router.get('/recent-activity', requireUser, async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 10;
    
    const recentActivity = await prisma.auditLog.findMany({
      take: limit,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: {
            id: true,
            email: true
          }
        }
      }
    });
    
    res.json(recentActivity);
    
  } catch (error) {
    console.error('Error fetching recent activity:', error);
    res.status(500).json({ error: 'Failed to fetch recent activity' });
  }
});

module.exports = router;
