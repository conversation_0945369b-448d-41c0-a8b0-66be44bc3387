/**
 * Server Entry Point
 * Starts the Express server using the app configuration
 */

require("dotenv").config();
const app = require('./app');

// Environment validation
if (!process.env.DATABASE_URL) {
  console.error("Error: DATABASE_URL variable in .env missing.");
  process.exit(-1);
}

const port = process.env.PORT || 3000;

app.listen(port, '0.0.0.0', () => {
  console.log(`Server running at http://0.0.0.0:${port}`);
  console.log(`Local: http://localhost:${port}`);
});
