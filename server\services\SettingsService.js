const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

class SettingsService {
  /**
   * Get all system settings grouped by category
   */
  async getAllSettings() {
    try {
      const settings = await prisma.systemSetting.findMany({
        orderBy: [
          { category: 'asc' },
          { key: 'asc' }
        ]
      });

      // Group settings by category
      const groupedSettings = settings.reduce((acc, setting) => {
        if (!acc[setting.category]) {
          acc[setting.category] = {};
        }
        
        // Parse JSON values if they exist
        let value = setting.value;
        try {
          value = JSON.parse(setting.value);
        } catch (e) {
          // Keep as string if not valid JSON
        }
        
        acc[setting.category][setting.key] = value;
        return acc;
      }, {});

      return groupedSettings;
    } catch (error) {
      throw new Error(`Failed to get settings: ${error.message}`);
    }
  }

  /**
   * Get settings by category
   */
  async getSettingsByCategory(category) {
    try {
      const settings = await prisma.systemSetting.findMany({
        where: { category },
        orderBy: { key: 'asc' }
      });

      const categorySettings = {};
      settings.forEach(setting => {
        let value = setting.value;
        try {
          value = JSON.parse(setting.value);
        } catch (e) {
          // Keep as string if not valid JSON
        }
        categorySettings[setting.key] = value;
      });

      return categorySettings;
    } catch (error) {
      throw new Error(`Failed to get settings for category ${category}: ${error.message}`);
    }
  }

  /**
   * Update multiple settings
   */
  async updateSettings(settingsData) {
    try {
      const operations = [];

      for (const [category, categorySettings] of Object.entries(settingsData)) {
        for (const [key, value] of Object.entries(categorySettings)) {
          const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
          
          operations.push(
            prisma.systemSetting.upsert({
              where: {
                key: `${category}.${key}`
              },
              update: {
                value: stringValue,
                updatedAt: new Date()
              },
              create: {
                key: `${category}.${key}`,
                value: stringValue,
                category: category,
                description: `${category} setting: ${key}`,
                isPublic: this.isPublicSetting(category, key)
              }
            })
          );
        }
      }

      await prisma.$transaction(operations);
      return { success: true, message: 'Settings updated successfully' };
    } catch (error) {
      throw new Error(`Failed to update settings: ${error.message}`);
    }
  }

  /**
   * Get a specific setting value
   */
  async getSetting(category, key) {
    try {
      const setting = await prisma.systemSetting.findUnique({
        where: {
          key: `${category}.${key}`
        }
      });

      if (!setting) {
        return null;
      }

      try {
        return JSON.parse(setting.value);
      } catch (e) {
        return setting.value;
      }
    } catch (error) {
      throw new Error(`Failed to get setting ${category}.${key}: ${error.message}`);
    }
  }

  /**
   * Set a specific setting value
   */
  async setSetting(category, key, value, description = null) {
    try {
      const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
      
      const setting = await prisma.systemSetting.upsert({
        where: {
          key: `${category}.${key}`
        },
        update: {
          value: stringValue,
          updatedAt: new Date()
        },
        create: {
          key: `${category}.${key}`,
          value: stringValue,
          category: category,
          description: description || `${category} setting: ${key}`,
          isPublic: this.isPublicSetting(category, key)
        }
      });

      return setting;
    } catch (error) {
      throw new Error(`Failed to set setting ${category}.${key}: ${error.message}`);
    }
  }

  /**
   * Initialize default settings if they don't exist
   */
  async initializeDefaultSettings() {
    try {
      const defaultSettings = {
        general: {
          siteName: 'WebCore System',
          siteDescription: 'Advanced Web Management System',
          logo: '',
          timezone: 'UTC',
          language: 'en',
          maintenanceMode: false
        },
        security: {
          passwordMinLength: 8,
          passwordRequireSpecial: true,
          sessionTimeout: 30,
          maxLoginAttempts: 5,
          twoFactorRequired: false,
          ipWhitelist: []
        },
        notifications: {
          emailEnabled: true,
          smsEnabled: false,
          pushEnabled: true,
          adminEmail: '<EMAIL>',
          smtpHost: '',
          smtpPort: 587,
          smtpUser: '',
          smtpPassword: ''
        },
        backup: {
          autoBackup: true,
          backupFrequency: 'daily',
          retentionDays: 30,
          backupLocation: '/backups'
        }
      };

      for (const [category, categorySettings] of Object.entries(defaultSettings)) {
        for (const [key, value] of Object.entries(categorySettings)) {
          const existingSetting = await prisma.systemSetting.findUnique({
            where: { key: `${category}.${key}` }
          });

          if (!existingSetting) {
            await this.setSetting(category, key, value);
          }
        }
      }

      return { success: true, message: 'Default settings initialized' };
    } catch (error) {
      throw new Error(`Failed to initialize default settings: ${error.message}`);
    }
  }

  /**
   * Determine if a setting should be public (accessible without authentication)
   */
  isPublicSetting(category, key) {
    const publicSettings = [
      'general.siteName',
      'general.siteDescription',
      'general.logo',
      'general.timezone',
      'general.language',
      'general.maintenanceMode'
    ];
    
    return publicSettings.includes(`${category}.${key}`);
  }
}

module.exports = new SettingsService();
