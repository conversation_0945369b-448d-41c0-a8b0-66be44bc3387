/**
 * Rate Limiting Middleware
 * حماية ضد هجمات Brute Force وDDoS
 */

const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');

/**
 * Rate limiter for login attempts
 * حد أقصى 50 محاولات تسجيل دخول كل 15 دقيقة (محسن للاختبار)
 */
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // limit each IP to 50 requests per windowMs (increased for testing)
  message: {
    error: '⚠️ محاولات تسجيل دخول كثيرة جداً. يرجى المحاولة مرة أخرى لاحقاً.',
    retryAfter: '15 دقيقة'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  skipSuccessfulRequests: true, // Don't count successful requests
  keyGenerator: (req) => {
    // Use IP + email for more granular limiting
    return `${req.ip}-${req.body?.email || 'unknown'}`;
  }
});

/**
 * Rate limiter for registration
 * حد أقصى 3 تسجيلات جديدة كل ساعة
 */
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // limit each IP to 3 registrations per hour
  message: {
    error: 'Too many registration attempts, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * General API rate limiter
 * حد أقصى 1000 طلب كل 15 دقيقة (محسن للاختبار)
 */
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // limit each IP to 1000 requests per windowMs (increased for testing)
  message: {
    error: '⚠️ طلبات كثيرة جداً. يرجى المحاولة مرة أخرى لاحقاً.',
    retryAfter: '15 دقيقة'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * Slow down middleware for repeated requests
 * تبطيء الطلبات المتكررة
 */
const speedLimiter = slowDown({
  windowMs: 15 * 60 * 1000, // 15 minutes
  delayAfter: 50, // allow 50 requests per 15 minutes at full speed
  delayMs: (used, req) => {
    const delayAfter = req.slowDown.limit;
    return (used - delayAfter) * 500;
  },
  maxDelayMs: 20000, // maximum delay of 20 seconds
});

/**
 * Strict rate limiter for sensitive operations
 * حد صارم للعمليات الحساسة
 */
const strictLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each IP to 10 requests per hour
  message: {
    error: 'Too many sensitive operations, please try again later.',
    retryAfter: '1 hour'
  },
  standardHeaders: true,
  legacyHeaders: false
});

module.exports = {
  loginLimiter,
  registerLimiter,
  apiLimiter,
  speedLimiter,
  strictLimiter
};
