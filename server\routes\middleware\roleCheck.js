const PrismaUserService = require('../../services/prismaUserService');

/**
 * Helper function to get user roles from request or database
 * @param {Object} user - User object from request
 * @returns {Promise<{userWithRoles: Object, userRoles: string[]}>}
 */
async function getUserRolesData(user) {
  let userWithRoles;
  let userRoles = [];

  // Check if user already has roles data (from auth middleware or tests)
  if (user.roles && Array.isArray(user.roles)) {
    userWithRoles = user;
    userRoles = user.roles.map(userRole =>
      userRole.role ? userRole.role.name : userRole.name
    );
  } else if (user.id) {
    // Get user with roles from database only if we have a valid ID
    userWithRoles = await PrismaUserService.getUserWithRoles(user.id);

    if (userWithRoles) {
      userRoles = userWithRoles.roles.map(userRole => userRole.role.name);
    }
  }

  return { userWithRoles, userRoles };
}

/**
 * Helper function to validate user authentication
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {boolean} - True if user is authenticated
 */
function validateUserAuth(req, res) {
  if (!req.user) {
    res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
    return false;
  }
  return true;
}

/**
 * Middleware to check if user has required role(s)
 * @param {string[]} requiredRoles - Array of role names that are allowed
 */
const checkRole = (requiredRoles) => {
  return async (req, res, next) => {
    try {
      if (!validateUserAuth(req, res)) return;

      const rolesArray = Array.isArray(requiredRoles) ? requiredRoles : [requiredRoles];
      const { userWithRoles, userRoles } = await getUserRolesData(req.user);

      if (!userWithRoles && req.user.id) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (!userRoles.length) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required role: ${rolesArray.join(', ')}`
        });
      }

      const hasRequiredRole = rolesArray.some(role => userRoles.includes(role));

      if (!hasRequiredRole) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required role: ${rolesArray.join(', ')}`
        });
      }

      req.userRoles = userRoles;
      req.userWithRoles = userWithRoles;
      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during role check'
      });
    }
  };
};

/**
 * Helper function to get user with roles for permission check
 * @param {Object} req - Express request object
 * @returns {Promise<Object|null>} - User with roles or null
 */
async function getUserWithRolesForPermission(req) {
  if (req.user.roles && Array.isArray(req.user.roles)) {
    return req.user;
  }

  if (req.userWithRoles) {
    return req.userWithRoles;
  }

  if (req.user.id) {
    return await PrismaUserService.getUserWithRoles(req.user.id);
  }

  return null;
}

/**
 * Helper function to check if user has specific permission
 * @param {Object} userWithRoles - User object with roles
 * @param {string} resource - Resource name
 * @param {string} action - Action name
 * @returns {boolean} - True if user has permission
 */
function hasUserPermission(userWithRoles, resource, action) {
  if (!userWithRoles.roles || !Array.isArray(userWithRoles.roles)) {
    return false;
  }

  for (const userRole of userWithRoles.roles) {
    const role = userRole.role || userRole;
    const rolePermissions = role.permissions;

    if (rolePermissions && rolePermissions[resource] &&
        Array.isArray(rolePermissions[resource]) &&
        rolePermissions[resource].includes(action)) {
      return true;
    }
  }

  return false;
}

/**
 * Middleware to check if user has specific permission
 * @param {string} resource - Resource name (e.g., 'users', 'roles')
 * @param {string} action - Action name (e.g., 'create', 'read', 'update', 'delete')
 */
const checkPermission = (resource, action) => {
  return async (req, res, next) => {
    try {
      if (!validateUserAuth(req, res)) return;

      const userWithRoles = await getUserWithRolesForPermission(req);

      if (!userWithRoles) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      if (!hasUserPermission(userWithRoles, resource, action)) {
        return res.status(403).json({
          success: false,
          message: `Access denied. Required permission: ${resource}.${action}`
        });
      }

      req.userRoles = userWithRoles.roles.map(ur =>
        ur.role ? ur.role.name : ur.name
      );
      req.userWithRoles = userWithRoles;
      next();
    } catch (error) {
      console.error('Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error during permission check'
      });
    }
  };
};

/**
 * Middleware to check if user is admin (has admin role)
 */
const requireAdmin = checkRole(['admin']);

/**
 * Middleware to check if user can manage roles
 */
const canManageRoles = checkPermission('roles', 'create');

module.exports = {
  checkRole,
  checkPermission,
  requireAdmin,
  canManageRoles
};
