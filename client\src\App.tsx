import { BrowserRouter as Router, Routes, Route } from "react-router-dom"
import { ThemeProvider } from "./components/ui/theme-provider"
import { Toaster } from "./components/ui/toaster"
import { AuthProvider } from "./contexts/AuthContext"
import { LanguageProvider } from "./contexts/LanguageContext"
import { SettingsProvider } from "./contexts/SettingsContext"
import { Login } from "./pages/Login"
import { Register } from "./pages/Register"
import { ProtectedRoute } from "./components/ProtectedRoute"
import { DashboardLayout } from "./components/DashboardLayout"
import { Dashboard } from "./pages/Dashboard"
import { UserManagement } from "./pages/UserManagement"
import { RoleManagement } from "./pages/RoleManagement"
import { RoleForm } from "./pages/RoleForm"
import { ModuleManagement } from "./pages/ModuleManagement"
import { AuditLogs } from "./pages/AuditLogs"
import { Integrations } from "./pages/Integrations"
import { SystemSettings } from "./pages/SystemSettings"
import { Notifications } from "./pages/Notifications"
import { Profile } from "./pages/Profile"
import { BlankPage } from "./pages/BlankPage"

function App() {
  return (
    <AuthProvider>
      <LanguageProvider>
        <SettingsProvider>
          <ThemeProvider defaultTheme="light" storageKey="ui-theme">
            <Router>
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route path="/register" element={<Register />} />
                <Route path="/" element={
                  <ProtectedRoute>
                    <DashboardLayout />
                  </ProtectedRoute>
                }>
                  <Route index element={<Dashboard />} />
                  <Route path="users" element={<UserManagement />} />
                  <Route path="role-management" element={<RoleManagement />} />
                  <Route path="role-management/new" element={<RoleForm />} />
                  <Route path="role-management/edit/:id" element={<RoleForm />} />
                  <Route path="modules" element={<ModuleManagement />} />
                  <Route path="audit" element={<AuditLogs />} />
                  <Route path="integrations" element={<Integrations />} />
                  <Route path="settings" element={<SystemSettings />} />
                  <Route path="notifications" element={<Notifications />} />
                  <Route path="profile" element={<Profile />} />
                  {/* Dynamic module routes */}
                  <Route path="analytics" element={<BlankPage />} />
                  <Route path="reports" element={<BlankPage />} />
                  <Route path="calendar" element={<BlankPage />} />
                </Route>
                <Route path="*" element={<BlankPage />} />
              </Routes>
            </Router>
            <Toaster />
          </ThemeProvider>
        </SettingsProvider>
      </LanguageProvider>
    </AuthProvider>
  )
}

export default App