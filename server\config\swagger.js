const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'WebCore API',
      version: '1.0.0',
      description: 'WebCore Management System API Documentation',
      contact: {
        name: 'WebCore Team',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Development server'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Authorization header using the Bearer scheme'
        }
      },
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique user identifier'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            isActive: {
              type: 'boolean',
              description: 'User active status'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'User creation timestamp'
            },
            lastLoginAt: {
              type: 'string',
              format: 'date-time',
              description: 'Last login timestamp'
            },
            roles: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/UserRole'
              }
            }
          }
        },
        Role: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'Unique role identifier'
            },
            name: {
              type: 'string',
              description: 'Role name'
            },
            description: {
              type: 'string',
              description: 'Role description'
            },
            permissions: {
              type: 'object',
              description: 'Role permissions object'
            },
            isActive: {
              type: 'boolean',
              description: 'Role active status'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        UserRole: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            userId: {
              type: 'string'
            },
            roleId: {
              type: 'string'
            },
            role: {
              $ref: '#/components/schemas/Role'
            }
          }
        },
        Module: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            name: {
              type: 'string',
              description: 'Module unique name'
            },
            displayName: {
              type: 'string',
              description: 'Module display name'
            },
            description: {
              type: 'string',
              description: 'Module description'
            },
            icon: {
              type: 'string',
              description: 'Module icon name'
            },
            route: {
              type: 'string',
              description: 'Module route path'
            },
            isActive: {
              type: 'boolean',
              description: 'Module active status'
            },
            sortOrder: {
              type: 'integer',
              description: 'Module sort order'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        AuditLog: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            userId: {
              type: 'string'
            },
            action: {
              type: 'string',
              description: 'Action performed'
            },
            resource: {
              type: 'string',
              description: 'Resource affected'
            },
            details: {
              type: 'object',
              description: 'Additional details'
            },
            ipAddress: {
              type: 'string',
              description: 'User IP address'
            },
            userAgent: {
              type: 'string',
              description: 'User agent string'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        SystemSetting: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            key: {
              type: 'string',
              description: 'Setting key'
            },
            value: {
              type: 'string',
              description: 'Setting value'
            },
            description: {
              type: 'string',
              description: 'Setting description'
            },
            category: {
              type: 'string',
              description: 'Setting category'
            },
            isPublic: {
              type: 'boolean',
              description: 'Is setting public'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            },
            updatedAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        Notification: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            title: {
              type: 'string',
              description: 'Notification title'
            },
            message: {
              type: 'string',
              description: 'Notification message'
            },
            type: {
              type: 'string',
              enum: ['info', 'warning', 'error', 'success'],
              description: 'Notification type'
            },
            isRead: {
              type: 'boolean',
              description: 'Is notification read'
            },
            userId: {
              type: 'string',
              description: 'Target user ID'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            }
          }
        },
        LoginRequest: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address'
            },
            password: {
              type: 'string',
              minLength: 6,
              description: 'User password'
            }
          }
        },
        LoginResponse: {
          type: 'object',
          properties: {
            id: {
              type: 'string'
            },
            email: {
              type: 'string'
            },
            isActive: {
              type: 'boolean'
            },
            createdAt: {
              type: 'string',
              format: 'date-time'
            },
            lastLoginAt: {
              type: 'string',
              format: 'date-time'
            },
            roles: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/UserRole'
              }
            },
            accessToken: {
              type: 'string',
              description: 'JWT access token'
            },
            refreshToken: {
              type: 'string',
              description: 'JWT refresh token'
            }
          }
        },
        RegisterRequest: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email'
            },
            password: {
              type: 'string',
              minLength: 6
            }
          }
        },
        RefreshTokenRequest: {
          type: 'object',
          required: ['refreshToken'],
          properties: {
            refreshToken: {
              type: 'string',
              description: 'JWT refresh token'
            }
          }
        },
        RefreshTokenResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean'
            },
            data: {
              type: 'object',
              properties: {
                accessToken: {
                  type: 'string'
                },
                refreshToken: {
                  type: 'string'
                }
              }
            }
          }
        },
        Error: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Error message'
            },
            error: {
              type: 'string',
              description: 'Error details'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ['./routes/*.js', './routes/**/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(options);

module.exports = {
  specs,
  swaggerUi
};
