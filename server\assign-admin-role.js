/**
 * Assign Admin Role to User
 * إضافة دور الإدارة للمستخدم
 */

const { prisma } = require('./config/prisma');

async function assignAdminRole() {
  try {
    console.log('🔧 إضافة دور الإدارة للمستخدم...');

    // Find admin user
    const adminUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    if (!adminUser) {
      console.error('❌ المستخدم الإداري غير موجود');
      return;
    }

    // Find admin role
    const adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });

    if (!adminRole) {
      console.error('❌ دور الإدارة غير موجود');
      return;
    }

    // Check if user already has admin role
    const hasAdminRole = adminUser.roles.some(userRole => userRole.roleId === adminRole.id);
    
    if (hasAdminRole) {
      console.log('✅ المستخدم يملك دور الإدارة بالفعل');
      return;
    }

    // Assign admin role to user using UserRole junction table
    await prisma.userRole.create({
      data: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    });

    console.log('✅ تم إضافة دور الإدارة للمستخدم بنجاح');
    console.log(`👤 المستخدم: ${adminUser.email}`);
    console.log(`🔑 الدور: ${adminRole.name} (${adminRole.description})`);

    // Verify the assignment
    const updatedUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: {
        roles: {
          include: {
            role: true
          }
        }
      }
    });

    console.log('📋 الأدوار الحالية:');
    updatedUser.roles.forEach(userRole => {
      console.log(`  - ${userRole.role.name}: ${userRole.role.description}`);
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة دور الإدارة:', error);
  } finally {
    await prisma.$disconnect();
  }
}

assignAdminRole();
