{"name": "node_server", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:security": "jest tests/security.test.js", "test:middleware": "jest tests/middleware.test.js", "test:routes": "jest tests/roleRoutes.test.js"}, "keywords": [], "author": "", "license": "ISC", "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/jest.setup.js"], "maxWorkers": 1, "runInBand": true, "forceExit": true, "detectOpenHandles": true, "testTimeout": 30000, "collectCoverageFrom": ["routes/**/*.js", "services/**/*.js", "!routes/middleware/auth.js", "!server.js", "!app.js"], "coverageThreshold": {"global": {"branches": 25, "functions": 20, "lines": 25, "statements": 25}}}, "dependencies": {"@anthropic-ai/sdk": "^0.27.3", "@prisma/client": "^6.12.0", "adm-zip": "^0.5.16", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chart.js": "^4.4.1", "compression": "^1.8.1", "connect-flash": "^0.1.1", "connect-mongo": "^5.1.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.4.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-session": "^1.18.0", "express-slow-down": "^2.1.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.1.1", "multer": "^2.0.2", "openai": "^4.63.0", "pg": "^8.16.3", "pino": "^9.5.0", "prisma": "^6.12.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.10", "supertest": "^7.1.4"}}