import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import {
  Settings,
  Shield,
  Bell,
  Database,
  Save,
  Loader2,
  Building,
  Palette,
  Mail,
  Upload,
  Eye,
  X,
  Globe,
  Phone,
  MapPin,
  Clock,
  FileText,
  Image
} from "lucide-react"
import { toast } from "@/hooks/useToast"
import { useSettings } from "@/contexts/SettingsContext"
import { 
  getSystemSettings, 
  updateSystemSettings, 
  getTimezones, 
  uploadLogo,
  getDefaultEmailTemplates,
  previewEmailTemplate,
  type SystemSettings as SystemSettingsType,
  type Timezone 
} from "@/api/settings"

// Default settings as a constant
const getDefaultSettings = (): SystemSettingsType => ({
  general: {
    siteName: "", // سيتم ملؤه من اسم الشركة تلقائياً
    siteDescription: "", // سيتم ملؤه من وصف الشركة تلقائياً
    logo: "",
    timezone: "UTC",
    language: "en",
    maintenanceMode: false
  },
  company: {
    name: {
      en: "WebCore Company",
      ar: "شركة ويب كور"
    },
    description: {
      en: "Advanced Web Management Solutions",
      ar: "حلول إدارة الويب المتقدمة"
    },
    address: {
      en: "",
      ar: ""
    },
    phone: "",
    website: "",
    supportEmail: "",
    businessHours: {
      en: "Monday - Friday: 9:00 AM - 6:00 PM",
      ar: "الاثنين - الجمعة: 9:00 ص - 6:00 م"
    },
    socialMedia: {
      facebook: "",
      twitter: "",
      linkedin: "",
      instagram: ""
    },
    legalInfo: {
      registrationNumber: "",
      taxId: "",
      license: ""
    }
  },
  branding: {
    primaryLogo: "",
    secondaryLogo: "",
    favicon: "",
    primaryColor: "#3b82f6",
    secondaryColor: "#64748b",
    accentColor: "#10b981",
    backgroundColor: "#ffffff",
    textColor: "#1e293b"
  },
  security: {
    passwordMinLength: 8,
    passwordRequireSpecial: true,
    sessionTimeout: 30,
    maxLoginAttempts: 5,
    twoFactorRequired: false,
    ipWhitelist: []
  },
  notifications: {
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    adminEmail: "<EMAIL>",
    smtpHost: "",
    smtpPort: 587,
    smtpUser: "",
    smtpPassword: ""
  },
  emailTemplates: getDefaultEmailTemplates(),
  backup: {
    autoBackup: true,
    backupFrequency: "daily",
    retentionDays: 30,
    backupLocation: "/backups"
  }
})

export function SystemSettingsEnhanced() {
  const [settings, setSettings] = useState<SystemSettingsType | null>(null)

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [timezones, setTimezones] = useState<Timezone[]>([])
  const [logoFiles, setLogoFiles] = useState<{[key: string]: File | null}>({
    primary: null,
    secondary: null,
    favicon: null
  })
  const [logoPreviews, setLogoPreviews] = useState<{[key: string]: string}>({
    primary: "",
    secondary: "",
    favicon: ""
  })
  const [emailPreview, setEmailPreview] = useState<{subject: string, body: string} | null>(null)
  const [previewLoading, setPreviewLoading] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<string>("")
  const [selectedLanguage, setSelectedLanguage] = useState<'en' | 'ar'>('en')

  const { refreshSettings } = useSettings()

  useEffect(() => {
    fetchSettings()
    fetchTimezones()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const data = await getSystemSettings()

      // Get default settings
      const defaultSettings = getDefaultSettings()

      if (data) {
        // Deep merge with default settings to ensure all properties exist
        const mergedSettings: SystemSettingsType = {
          general: {
            ...defaultSettings.general,
            ...data.general
          },
          company: {
            name: {
              en: data.company?.name?.en || defaultSettings.company.name.en,
              ar: data.company?.name?.ar || defaultSettings.company.name.ar
            },
            description: {
              en: data.company?.description?.en || defaultSettings.company.description.en,
              ar: data.company?.description?.ar || defaultSettings.company.description.ar
            },
            address: {
              en: data.company?.address?.en || defaultSettings.company.address.en,
              ar: data.company?.address?.ar || defaultSettings.company.address.ar
            },
            phone: data.company?.phone || defaultSettings.company.phone,
            website: data.company?.website || defaultSettings.company.website,
            supportEmail: data.company?.supportEmail || defaultSettings.company.supportEmail,
            businessHours: {
              en: data.company?.businessHours?.en || defaultSettings.company.businessHours.en,
              ar: data.company?.businessHours?.ar || defaultSettings.company.businessHours.ar
            },
            socialMedia: {
              ...defaultSettings.company.socialMedia,
              ...data.company?.socialMedia
            },
            legalInfo: {
              ...defaultSettings.company.legalInfo,
              ...data.company?.legalInfo
            }
          },
          branding: {
            ...defaultSettings.branding,
            ...data.branding
          },
          security: {
            ...defaultSettings.security,
            ...data.security,
            ipWhitelist: Array.isArray(data.security?.ipWhitelist) ? data.security.ipWhitelist : []
          },
          notifications: {
            ...defaultSettings.notifications,
            ...data.notifications
          },
          emailTemplates: {
            ...defaultSettings.emailTemplates,
            ...data.emailTemplates
          },
          backup: {
            ...defaultSettings.backup,
            ...data.backup
          }
        }

        setSettings(mergedSettings)

        // Set logo previews if they exist
        if (mergedSettings.branding.primaryLogo) {
          setLogoPreviews(prev => ({ ...prev, primary: mergedSettings.branding.primaryLogo }))
        }
        if (mergedSettings.branding.secondaryLogo) {
          setLogoPreviews(prev => ({ ...prev, secondary: mergedSettings.branding.secondaryLogo }))
        }
        if (mergedSettings.branding.favicon) {
          setLogoPreviews(prev => ({ ...prev, favicon: mergedSettings.branding.favicon }))
        }
      } else {
        // If no data from server, use defaults
        setSettings(defaultSettings)
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      // On error, use default settings
      setSettings(getDefaultSettings())
      toast({
        title: "Error",
        description: "Failed to load settings. Using default values.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const fetchTimezones = async () => {
    try {
      const data = await getTimezones()
      setTimezones(data)
    } catch (error) {
      console.error('Error fetching timezones:', error)
    }
  }

  const updateSetting = (category: keyof SystemSettingsType, key: string, value: any, subKey?: string) => {
    if (!settings) return

    setSettings(prev => {
      if (!prev) return prev

      const newSettings = {
        ...prev,
        [category]: {
          ...prev[category],
          [key]: subKey ? {
            ...(prev[category] as any)[key],
            [subKey]: value
          } : value
        }
      }

      // Auto-update site name and description when company info changes
      if (category === 'company' && key === 'name') {
        newSettings.general = {
          ...newSettings.general,
          siteName: subKey === 'en' ? value : newSettings.company.name.en
        }
      }

      if (category === 'company' && key === 'description') {
        newSettings.general = {
          ...newSettings.general,
          siteDescription: subKey === 'en' ? value : newSettings.company.description.en
        }
      }

      return newSettings
    })
  }

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>, logoType: 'primary' | 'secondary' | 'favicon') => {
    const file = event.target.files?.[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast({
          title: "Error",
          description: "File size must be less than 5MB",
          variant: "destructive",
        })
        return
      }

      if (!file.type.startsWith('image/')) {
        toast({
          title: "Error",
          description: "Please select a valid image file",
          variant: "destructive",
        })
        return
      }

      setLogoFiles(prev => ({ ...prev, [logoType]: file }))
      const reader = new FileReader()
      reader.onload = (e) => {
        const result = e.target?.result as string
        setLogoPreviews(prev => ({ ...prev, [logoType]: result }))
        updateSetting('branding', `${logoType}Logo`, result)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeLogo = (logoType: 'primary' | 'secondary' | 'favicon') => {
    setLogoFiles(prev => ({ ...prev, [logoType]: null }))
    setLogoPreviews(prev => ({ ...prev, [logoType]: "" }))
    updateSetting('branding', `${logoType}Logo`, "")
  }

  const handleEmailTemplatePreview = async (templateKey: string, language: 'en' | 'ar') => {
    try {
      setPreviewLoading(true)
      const template = settings.emailTemplates[templateKey as keyof typeof settings.emailTemplates]
      const preview = await previewEmailTemplate({
        templateType: templateKey,
        subject: template.subject[language],
        body: template.body[language],
        language: language
      })
      setEmailPreview(preview.preview)
    } catch (error) {
      console.error('Error previewing email:', error)
      toast({
        title: "Error",
        description: "Failed to preview email template",
        variant: "destructive",
      })
    } finally {
      setPreviewLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      await updateSystemSettings(settings)
      await refreshSettings()
      toast({
        title: "Success",
        description: "Settings saved successfully",
      })
    } catch (error) {
      console.error('Error saving settings:', error)
      toast({
        title: "Error",
        description: "Failed to save settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  if (loading || !settings) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">System Settings</h1>
          <p className="text-muted-foreground">
            Configure your system preferences and company information
          </p>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="company" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            Company
          </TabsTrigger>
          <TabsTrigger value="branding" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Branding
          </TabsTrigger>
          <TabsTrigger value="email-templates" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email Templates
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
        </TabsList>

        {/* General Settings Tab */}
        <TabsContent value="general">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                General Settings
              </CardTitle>
              <CardDescription>
                Basic system configuration and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* System Information Display */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 p-4 rounded-lg border">
                <h4 className="font-medium text-sm text-blue-900 dark:text-blue-100 mb-3">System Information</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Site Name:</span>
                    <p className="font-medium">{settings?.company.name[settings?.general.language as 'en' | 'ar'] || 'Not Set'}</p>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Description:</span>
                    <p className="font-medium">{settings?.company.description[settings?.general.language as 'en' | 'ar'] || 'Not Set'}</p>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground mt-2">
                  💡 Site name and description are automatically taken from Company Information
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="language">Default Language</Label>
                  <Select
                    value={settings?.general.language}
                    onValueChange={(value) => updateSetting('general', 'language', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="ar">العربية</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timezone">Timezone</Label>
                  <Select
                    value={settings?.general.timezone}
                    onValueChange={(value) => updateSetting('general', 'timezone', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      {timezones.map((tz) => (
                        <SelectItem key={tz.value} value={tz.value}>
                          {tz.label} ({tz.offset})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Maintenance Mode</Label>
                  <p className="text-sm text-muted-foreground">
                    Enable maintenance mode to restrict access to the system
                  </p>
                </div>
                <Switch
                  checked={settings?.general.maintenanceMode}
                  onCheckedChange={(checked) => updateSetting('general', 'maintenanceMode', checked)}
                />
              </div>

              <div className="bg-amber-50 dark:bg-amber-950/20 p-4 rounded-lg border border-amber-200 dark:border-amber-800">
                <div className="flex items-start gap-3">
                  <div className="p-1 rounded-full bg-amber-100 dark:bg-amber-900">
                    <Building className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-amber-900 dark:text-amber-100 text-sm">Quick Setup Tip</h4>
                    <p className="text-amber-700 dark:text-amber-300 text-sm mt-1">
                      To complete your system setup, make sure to fill in your company information in the "Company" tab.
                      This will automatically populate the site name and description.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Company Information Tab */}
        <TabsContent value="company">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Company Information
              </CardTitle>
              <CardDescription>
                Configure your company details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Company Name */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Company Name</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyNameEn">English</Label>
                    <Input
                      id="companyNameEn"
                      value={settings.company.name.en}
                      onChange={(e) => updateSetting('company', 'name', e.target.value, 'en')}
                      placeholder="Company name in English"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companyNameAr">Arabic</Label>
                    <Input
                      id="companyNameAr"
                      value={settings.company.name.ar}
                      onChange={(e) => updateSetting('company', 'name', e.target.value, 'ar')}
                      placeholder="اسم الشركة بالعربية"
                      dir="rtl"
                    />
                  </div>
                </div>
              </div>

              {/* Company Description */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Company Description</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyDescEn">English</Label>
                    <Textarea
                      id="companyDescEn"
                      value={settings.company.description.en}
                      onChange={(e) => updateSetting('company', 'description', e.target.value, 'en')}
                      placeholder="Company description in English"
                      rows={3}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companyDescAr">Arabic</Label>
                    <Textarea
                      id="companyDescAr"
                      value={settings.company.description.ar}
                      onChange={(e) => updateSetting('company', 'description', e.target.value, 'ar')}
                      placeholder="وصف الشركة بالعربية"
                      rows={3}
                      dir="rtl"
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Contact Information */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Contact Information</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Phone Number
                    </Label>
                    <Input
                      id="phone"
                      value={settings.company.phone}
                      onChange={(e) => updateSetting('company', 'phone', e.target.value)}
                      placeholder="+****************"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="website" className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Website
                    </Label>
                    <Input
                      id="website"
                      value={settings.company.website}
                      onChange={(e) => updateSetting('company', 'website', e.target.value)}
                      placeholder="https://www.company.com"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="supportEmail" className="flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    Support Email
                  </Label>
                  <Input
                    id="supportEmail"
                    type="email"
                    value={settings.company.supportEmail}
                    onChange={(e) => updateSetting('company', 'supportEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Branding Tab */}
        <TabsContent value="branding">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                Branding & Visual Identity
              </CardTitle>
              <CardDescription>
                Customize your brand appearance with logos, colors, and visual elements
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Logo Management */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Logo Management</Label>

                {/* Primary Logo */}
                <div className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    <Label className="font-medium">Primary Logo</Label>
                  </div>
                  <div className="flex items-start gap-4">
                    {logoPreviews.primary && (
                      <div className="relative">
                        <img
                          src={logoPreviews.primary}
                          alt="Primary Logo Preview"
                          className="w-24 h-24 object-contain border rounded"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLogo('primary')}
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 text-white hover:bg-red-600"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                    <div className="space-y-2">
                      <Label htmlFor="primary-logo-upload" className="cursor-pointer">
                        <Button variant="outline" className="flex items-center gap-2" asChild>
                          <span>
                            <Upload className="h-4 w-4" />
                            Upload Primary Logo
                          </span>
                        </Button>
                      </Label>
                      <Input
                        id="primary-logo-upload"
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleLogoUpload(e, 'primary')}
                        className="hidden"
                      />
                      <p className="text-xs text-muted-foreground">
                        PNG, JPG, SVG up to 5MB. Recommended: 200x60px
                      </p>
                    </div>
                  </div>
                </div>

                {/* Secondary Logo */}
                <div className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    <Label className="font-medium">Secondary Logo (Optional)</Label>
                  </div>
                  <div className="flex items-start gap-4">
                    {logoPreviews.secondary && (
                      <div className="relative">
                        <img
                          src={logoPreviews.secondary}
                          alt="Secondary Logo Preview"
                          className="w-24 h-24 object-contain border rounded"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLogo('secondary')}
                          className="absolute -top-2 -right-2 h-6 w-6 rounded-full bg-red-500 text-white hover:bg-red-600"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    )}
                    <div className="space-y-2">
                      <Label htmlFor="secondary-logo-upload" className="cursor-pointer">
                        <Button variant="outline" className="flex items-center gap-2" asChild>
                          <span>
                            <Upload className="h-4 w-4" />
                            Upload Secondary Logo
                          </span>
                        </Button>
                      </Label>
                      <Input
                        id="secondary-logo-upload"
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleLogoUpload(e, 'secondary')}
                        className="hidden"
                      />
                      <p className="text-xs text-muted-foreground">
                        For dark backgrounds or alternative uses
                      </p>
                    </div>
                  </div>
                </div>

                {/* Favicon */}
                <div className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center gap-2">
                    <Image className="h-4 w-4" />
                    <Label className="font-medium">Favicon</Label>
                  </div>
                  <div className="flex items-start gap-4">
                    {logoPreviews.favicon && (
                      <div className="relative">
                        <img
                          src={logoPreviews.favicon}
                          alt="Favicon Preview"
                          className="w-8 h-8 object-contain border rounded"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLogo('favicon')}
                          className="absolute -top-2 -right-2 h-4 w-4 rounded-full bg-red-500 text-white hover:bg-red-600"
                        >
                          <X className="h-2 w-2" />
                        </Button>
                      </div>
                    )}
                    <div className="space-y-2">
                      <Label htmlFor="favicon-upload" className="cursor-pointer">
                        <Button variant="outline" className="flex items-center gap-2" asChild>
                          <span>
                            <Upload className="h-4 w-4" />
                            Upload Favicon
                          </span>
                        </Button>
                      </Label>
                      <Input
                        id="favicon-upload"
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleLogoUpload(e, 'favicon')}
                        className="hidden"
                      />
                      <p className="text-xs text-muted-foreground">
                        ICO, PNG up to 1MB. Recommended: 32x32px
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Color Scheme */}
              <div className="space-y-4">
                <Label className="text-base font-medium">Color Scheme</Label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="primaryColor">Primary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={settings.branding.primaryColor}
                        onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={settings.branding.primaryColor}
                        onChange={(e) => updateSetting('branding', 'primaryColor', e.target.value)}
                        placeholder="#3b82f6"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="secondaryColor">Secondary Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="secondaryColor"
                        type="color"
                        value={settings.branding.secondaryColor}
                        onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={settings.branding.secondaryColor}
                        onChange={(e) => updateSetting('branding', 'secondaryColor', e.target.value)}
                        placeholder="#64748b"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accentColor">Accent Color</Label>
                    <div className="flex gap-2">
                      <Input
                        id="accentColor"
                        type="color"
                        value={settings.branding.accentColor}
                        onChange={(e) => updateSetting('branding', 'accentColor', e.target.value)}
                        className="w-12 h-10 p-1 border rounded"
                      />
                      <Input
                        value={settings.branding.accentColor}
                        onChange={(e) => updateSetting('branding', 'accentColor', e.target.value)}
                        placeholder="#10b981"
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Email Templates Tab */}
        <TabsContent value="email-templates">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                Email Templates
              </CardTitle>
              <CardDescription>
                Configure email templates for system notifications and user communications
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Language Selector */}
              <div className="flex items-center gap-4">
                <Label>Edit templates in:</Label>
                <Select
                  value={selectedLanguage}
                  onValueChange={(value: 'en' | 'ar') => setSelectedLanguage(value)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="en">English</SelectItem>
                    <SelectItem value="ar">العربية</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Email Templates */}
              {Object.entries(settings.emailTemplates).map(([templateKey, template]) => (
                <Card key={templateKey} className="border border-slate-200 dark:border-slate-700">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
                          <FileText className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            {templateKey === 'welcome' && 'Welcome Email'}
                            {templateKey === 'passwordReset' && 'Password Reset'}
                            {templateKey === 'accountSuspended' && 'Account Suspended'}
                            {templateKey === 'systemAlert' && 'System Alert'}
                          </CardTitle>
                          <CardDescription className="text-sm">
                            {templateKey === 'welcome' && 'Sent when a new user account is created'}
                            {templateKey === 'passwordReset' && 'Sent when user requests password reset'}
                            {templateKey === 'accountSuspended' && 'Sent when user account is suspended'}
                            {templateKey === 'systemAlert' && 'Sent for system alerts and notifications'}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={template.enabled ? "default" : "secondary"}>
                          {template.enabled ? "Enabled" : "Disabled"}
                        </Badge>
                        <Switch
                          checked={template.enabled}
                          onCheckedChange={(checked) =>
                            setSettings(prev => ({
                              ...prev,
                              emailTemplates: {
                                ...prev.emailTemplates,
                                [templateKey]: {
                                  ...prev.emailTemplates[templateKey as keyof typeof prev.emailTemplates],
                                  enabled: checked
                                }
                              }
                            }))
                          }
                        />
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor={`${templateKey}-subject-${selectedLanguage}`}>
                        Email Subject ({selectedLanguage === 'en' ? 'English' : 'Arabic'})
                      </Label>
                      <Input
                        id={`${templateKey}-subject-${selectedLanguage}`}
                        value={template.subject[selectedLanguage]}
                        onChange={(e) =>
                          setSettings(prev => ({
                            ...prev,
                            emailTemplates: {
                              ...prev.emailTemplates,
                              [templateKey]: {
                                ...prev.emailTemplates[templateKey as keyof typeof prev.emailTemplates],
                                subject: {
                                  ...prev.emailTemplates[templateKey as keyof typeof prev.emailTemplates].subject,
                                  [selectedLanguage]: e.target.value
                                }
                              }
                            }
                          }))
                        }
                        placeholder="Enter email subject..."
                        dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`${templateKey}-body-${selectedLanguage}`}>
                        Email Body ({selectedLanguage === 'en' ? 'English' : 'Arabic'})
                      </Label>
                      <Textarea
                        id={`${templateKey}-body-${selectedLanguage}`}
                        value={template.body[selectedLanguage]}
                        onChange={(e) =>
                          setSettings(prev => ({
                            ...prev,
                            emailTemplates: {
                              ...prev.emailTemplates,
                              [templateKey]: {
                                ...prev.emailTemplates[templateKey as keyof typeof prev.emailTemplates],
                                body: {
                                  ...prev.emailTemplates[templateKey as keyof typeof prev.emailTemplates].body,
                                  [selectedLanguage]: e.target.value
                                }
                              }
                            }
                          }))
                        }
                        rows={8}
                        placeholder="Enter email body content..."
                        className="font-mono text-sm"
                        dir={selectedLanguage === 'ar' ? 'rtl' : 'ltr'}
                      />
                      <div className="text-xs text-slate-500 space-y-1">
                        <p>Available placeholders:</p>
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline" className="text-xs">{'{{name}}'}</Badge>
                          <Badge variant="outline" className="text-xs">{'{{companyName}}'}</Badge>
                          {templateKey === 'passwordReset' && (
                            <Badge variant="outline" className="text-xs">{'{{resetLink}}'}</Badge>
                          )}
                          {templateKey === 'systemAlert' && (
                            <>
                              <Badge variant="outline" className="text-xs">{'{{alertType}}'}</Badge>
                              <Badge variant="outline" className="text-xs">{'{{message}}'}</Badge>
                              <Badge variant="outline" className="text-xs">{'{{timestamp}}'}</Badge>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleEmailTemplatePreview(templateKey, selectedLanguage)}
                        disabled={previewLoading}
                        className="flex items-center gap-2"
                      >
                        {previewLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                        Preview
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}

              {/* Email Preview Modal */}
              {emailPreview && (
                <Card className="border-2 border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Email Preview</CardTitle>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEmailPreview(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label className="font-medium">Subject:</Label>
                      <div className="p-3 bg-white dark:bg-slate-800 rounded border">
                        {emailPreview.subject}
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="font-medium">Body:</Label>
                      <div className="p-3 bg-white dark:bg-slate-800 rounded border whitespace-pre-wrap">
                        {emailPreview.body}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Security Settings
              </CardTitle>
              <CardDescription>
                Configure security policies and access controls
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    min="6"
                    max="32"
                    value={settings.security.passwordMinLength}
                    onChange={(e) => updateSetting('security', 'passwordMinLength', parseInt(e.target.value))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    min="5"
                    max="1440"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    min="3"
                    max="10"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Require Special Characters</Label>
                    <p className="text-sm text-muted-foreground">
                      Passwords must contain special characters
                    </p>
                  </div>
                  <Switch
                    checked={settings.security.passwordRequireSpecial}
                    onCheckedChange={(checked) => updateSetting('security', 'passwordRequireSpecial', checked)}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Two-Factor Authentication</Label>
                  <p className="text-sm text-muted-foreground">
                    Require 2FA for all user accounts
                  </p>
                </div>
                <Switch
                  checked={settings.security.twoFactorRequired}
                  onCheckedChange={(checked) => updateSetting('security', 'twoFactorRequired', checked)}
                />
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="ipWhitelist">IP Whitelist (one per line)</Label>
                <Textarea
                  id="ipWhitelist"
                  value={settings.security.ipWhitelist?.join('\n') || ''}
                  onChange={(e) => updateSetting('security', 'ipWhitelist', e.target.value.split('\n').filter(ip => ip.trim()))}
                  placeholder="***********&#10;10.0.0.0/8"
                  rows={4}
                />
                <p className="text-sm text-muted-foreground">
                  Leave empty to allow all IPs. Use CIDR notation for ranges.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications">
          <Card className="bg-white/50 backdrop-blur-sm dark:bg-slate-900/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Settings
              </CardTitle>
              <CardDescription>
                Configure notification channels and SMTP settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Email Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable email notifications for system events
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.emailEnabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'emailEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>SMS Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable SMS notifications for critical alerts
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.smsEnabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'smsEnabled', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Push Notifications</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable browser push notifications
                    </p>
                  </div>
                  <Switch
                    checked={settings.notifications.pushEnabled}
                    onCheckedChange={(checked) => updateSetting('notifications', 'pushEnabled', checked)}
                  />
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="text-sm font-medium">SMTP Configuration</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="smtpHost">SMTP Host</Label>
                    <Input
                      id="smtpHost"
                      value={settings.notifications.smtpHost}
                      onChange={(e) => updateSetting('notifications', 'smtpHost', e.target.value)}
                      placeholder="smtp.gmail.com"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtpPort">SMTP Port</Label>
                    <Input
                      id="smtpPort"
                      type="number"
                      value={settings.notifications.smtpPort}
                      onChange={(e) => updateSetting('notifications', 'smtpPort', parseInt(e.target.value))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="adminEmail">Admin Email</Label>
                  <Input
                    id="adminEmail"
                    type="email"
                    value={settings.notifications.adminEmail}
                    onChange={(e) => updateSetting('notifications', 'adminEmail', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="smtpUser">SMTP Username</Label>
                    <Input
                      id="smtpUser"
                      value={settings.notifications.smtpUser}
                      onChange={(e) => updateSetting('notifications', 'smtpUser', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="smtpPassword">SMTP Password</Label>
                    <Input
                      id="smtpPassword"
                      type="password"
                      value={settings.notifications.smtpPassword}
                      onChange={(e) => updateSetting('notifications', 'smtpPassword', e.target.value)}
                      placeholder="••••••••"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="flex justify-end">
        <Button onClick={handleSave} disabled={saving} size="lg">
          {saving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Settings
            </>
          )}
        </Button>
      </div>
    </div>
  )
}
