const mongoose = require('mongoose');
const { prisma } = require('../config/prisma.js');
require('dotenv').config();

// MongoDB connection string (original database)
const MONGODB_URL = 'mongodb://localhost/WebCore';

// MongoDB User schema (for reading existing data)
const mongoUserSchema = new mongoose.Schema({
  email: String,
  password: String,
  createdAt: Date,
  lastLoginAt: Date,
  isActive: Boolean,
  refreshToken: String,
});

const MongoUser = mongoose.model('User', mongoUserSchema);

async function migrateUsers() {
  console.log('🚀 Starting user migration from MongoDB to PostgreSQL...');
  
  try {
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URL);
    console.log('✅ Connected to MongoDB');

    // Connect to PostgreSQL via Prisma
    console.log('📡 Connecting to PostgreSQL...');
    await prisma.$connect();
    console.log('✅ Connected to PostgreSQL');

    // Get all users from MongoDB
    console.log('📊 Fetching users from MongoDB...');
    const mongoUsers = await MongoUser.find({});
    console.log(`📋 Found ${mongoUsers.length} users in MongoDB`);

    if (mongoUsers.length === 0) {
      console.log('ℹ️  No users found in MongoDB. Creating sample data...');
      await createSampleData();
      return;
    }

    // Migrate each user to PostgreSQL
    let migratedCount = 0;
    let skippedCount = 0;

    for (const mongoUser of mongoUsers) {
      try {
        // Check if user already exists in PostgreSQL
        const existingUser = await prisma.user.findUnique({
          where: { email: mongoUser.email }
        });

        if (existingUser) {
          console.log(`⏭️  User ${mongoUser.email} already exists, skipping...`);
          skippedCount++;
          continue;
        }

        // Create user in PostgreSQL
        const newUser = await prisma.user.create({
          data: {
            email: mongoUser.email,
            password: mongoUser.password,
            createdAt: mongoUser.createdAt || new Date(),
            lastLoginAt: mongoUser.lastLoginAt || new Date(),
            isActive: mongoUser.isActive !== undefined ? mongoUser.isActive : true,
            refreshToken: mongoUser.refreshToken || null,
          }
        });

        console.log(`✅ Migrated user: ${newUser.email}`);
        migratedCount++;

      } catch (error) {
        console.error(`❌ Error migrating user ${mongoUser.email}:`, error.message);
      }
    }

    console.log(`\n📊 Migration Summary:`);
    console.log(`   ✅ Successfully migrated: ${migratedCount} users`);
    console.log(`   ⏭️  Skipped (already exists): ${skippedCount} users`);
    console.log(`   📋 Total processed: ${mongoUsers.length} users`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    // Close connections
    await mongoose.disconnect();
    await prisma.$disconnect();
    console.log('🔌 Database connections closed');
  }
}

async function createSampleData() {
  console.log('🎯 Creating sample data for testing...');
  
  try {
    // Create sample admin user
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: "password"
        isActive: true,
      }
    });

    console.log(`✅ Created admin user: ${adminUser.email}`);

    // Create sample regular user
    const regularUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password: "password"
        isActive: true,
      }
    });

    console.log(`✅ Created regular user: ${regularUser.email}`);

    // Create sample roles
    const adminRole = await prisma.role.create({
      data: {
        name: 'admin',
        description: 'System Administrator',
        permissions: {
          users: ['create', 'read', 'update', 'delete'],
          roles: ['create', 'read', 'update', 'delete'],
          modules: ['create', 'read', 'update', 'delete'],
          settings: ['create', 'read', 'update', 'delete']
        }
      }
    });

    const userRole = await prisma.role.create({
      data: {
        name: 'user',
        description: 'Regular User',
        permissions: {
          profile: ['read', 'update'],
          dashboard: ['read']
        }
      }
    });

    console.log(`✅ Created roles: admin, user`);

    // Assign roles to users
    await prisma.userRole.create({
      data: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    });

    await prisma.userRole.create({
      data: {
        userId: regularUser.id,
        roleId: userRole.id
      }
    });

    console.log(`✅ Assigned roles to users`);

    // Create sample modules
    const modules = [
      { name: 'dashboard', displayName: 'Dashboard', route: '/', icon: 'home' },
      { name: 'users', displayName: 'User Management', route: '/users', icon: 'users' },
      { name: 'roles', displayName: 'Role Management', route: '/roles', icon: 'shield' },
      { name: 'modules', displayName: 'Module Management', route: '/modules', icon: 'grid' },
      { name: 'audit', displayName: 'Audit Logs', route: '/audit', icon: 'file-text' },
      { name: 'settings', displayName: 'System Settings', route: '/settings', icon: 'settings' },
    ];

    for (const module of modules) {
      await prisma.module.create({ data: module });
    }

    console.log(`✅ Created ${modules.length} sample modules`);

    console.log('\n🎉 Sample data created successfully!');
    console.log('\n🔑 Test Credentials:');
    console.log('   Admin: <EMAIL> / password');
    console.log('   User:  <EMAIL> / password');

  } catch (error) {
    console.error('❌ Error creating sample data:', error);
  }
}

// Run migration
if (require.main === module) {
  migrateUsers()
    .then(() => {
      console.log('🎉 Migration completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateUsers, createSampleData };
