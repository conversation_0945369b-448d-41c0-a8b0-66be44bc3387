const { randomUUID } = require('crypto');
const { prisma } = require('../config/prisma.js');
const { generatePasswordHash, validatePassword } = require('../utils/password.js');

class PrismaUserService {
  static async list() {
    try {
      return await prisma.user.findMany({
        select: {
          id: true,
          email: true,
          createdAt: true,
          lastLoginAt: true,
          isActive: true,
          roles: {
            include: {
              role: true
            }
          }
        }
      });
    } catch (err) {
      throw new Error(`Database error while listing users: ${err.message}`);
    }
  }

  static async get(id) {
    try {
      return await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          createdAt: true,
          lastLoginAt: true,
          isActive: true,
          refreshToken: true,
          roles: {
            include: {
              role: true
            }
          }
        }
      });
    } catch (err) {
      throw new Error(`Database error while getting user by ID: ${err.message}`);
    }
  }

  static async getByEmail(email) {
    try {
      return await prisma.user.findUnique({
        where: { email },
        select: {
          id: true,
          email: true,
          password: true,
          isActive: true,
          createdAt: true,
          lastLoginAt: true,
          refreshToken: true,
          roles: {
            select: {
              role: {
                select: {
                  id: true,
                  name: true,
                  permissions: true,
                  isActive: true
                }
              }
            }
          }
        }
      });
    } catch (err) {
      throw new Error(`Database error while getting user by email: ${err.message}`);
    }
  }

  static async update(id, data) {
    try {
      const updateData = { ...data };
      delete updateData.id; // Remove id from update data
      
      return await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          email: true,
          createdAt: true,
          lastLoginAt: true,
          isActive: true,
          refreshToken: true
        }
      });
    } catch (err) {
      throw new Error(`Database error while updating user ${id}: ${err.message}`);
    }
  }

  static async delete(id) {
    try {
      await prisma.user.delete({
        where: { id }
      });
      return true;
    } catch (err) {
      if (err.code === 'P2025') {
        return false; // User not found
      }
      throw new Error(`Database error while deleting user ${id}: ${err.message}`);
    }
  }

  static async authenticateWithPassword(email, password) {
    if (!email) throw new Error('Email is required');
    if (!password) throw new Error('Password is required');

    try {
      const user = await prisma.user.findUnique({
        where: { email },
        include: {
          roles: {
            include: {
              role: true
            }
          }
        }
      });

      if (!user) return null;

      const passwordValid = await validatePassword(password, user.password);
      if (!passwordValid) return null;

      // Update last login time
      const updatedUser = await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() },
        select: {
          id: true,
          email: true,
          createdAt: true,
          lastLoginAt: true,
          isActive: true,
          refreshToken: true,
          roles: {
            include: {
              role: true
            }
          }
        }
      });

      return updatedUser;
    } catch (err) {
      throw new Error(`Database error while authenticating user ${email}: ${err.message}`);
    }
  }

  static async create({ email, password, name = '' }) {
    if (!email) throw new Error('Email is required');
    if (!password) throw new Error('Password is required');

    const existingUser = await this.getByEmail(email);
    if (existingUser) throw new Error('User with this email already exists');

    const hash = await generatePasswordHash(password);

    try {
      const user = await prisma.user.create({
        data: {
          email,
          password: hash,
          refreshToken: randomUUID()
        },
        select: {
          id: true,
          email: true,
          createdAt: true,
          lastLoginAt: true,
          isActive: true,
          refreshToken: true
        }
      });

      return user;
    } catch (err) {
      throw new Error(`Database error while creating new user: ${err.message}`);
    }
  }

  static async setPassword(userId, password) {
    if (!password) throw new Error('Password is required');
    
    const hash = await generatePasswordHash(password);

    try {
      return await prisma.user.update({
        where: { id: userId },
        data: { password: hash },
        select: {
          id: true,
          email: true,
          createdAt: true,
          lastLoginAt: true,
          isActive: true
        }
      });
    } catch (err) {
      throw new Error(`Database error while setting password for user ${userId}: ${err.message}`);
    }
  }

  static async updateRefreshToken(userId, refreshToken) {
    try {
      return await prisma.user.update({
        where: { id: userId },
        data: { refreshToken },
        select: {
          id: true,
          email: true,
          refreshToken: true
        }
      });
    } catch (err) {
      throw new Error(`Database error while updating refresh token: ${err.message}`);
    }
  }

  static async findByRefreshToken(refreshToken) {
    try {
      return await prisma.user.findUnique({
        where: { refreshToken },
        select: {
          id: true,
          email: true,
          isActive: true,
          refreshToken: true
        }
      });
    } catch (err) {
      throw new Error(`Database error while finding user by refresh token: ${err.message}`);
    }
  }

  static async getUserWithRoles(userId) {
    try {
      return await prisma.user.findUnique({
        where: { id: userId },
        include: {
          roles: {
            include: {
              role: true
            }
          }
        }
      });
    } catch (err) {
      throw new Error(`Database error while getting user with roles: ${err.message}`);
    }
  }

  // Helper method to safely return user data (without password)
  static sanitizeUser(user) {
    if (!user) return null;
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }
}

module.exports = PrismaUserService;
