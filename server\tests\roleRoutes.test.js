const request = require('supertest');
const app = require('../app');
const { setupTestDatabase } = require('./setup');

describe('Role Routes Integration Tests', () => {
  let testData;
  let adminToken;
  let userToken;

  beforeEach(async () => {
    testData = await setupTestDatabase();

    // Get admin token
    const adminLoginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword'
      });
    adminToken = adminLoginResponse.body.accessToken;

    // Get user token
    const userLoginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'testpassword'
      });
    userToken = userLoginResponse.body.accessToken;
  });

  describe('GET /api/roles', () => {
    test('should allow admin to fetch roles', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    test('should deny regular user access to fetch roles', async () => {
      const response = await request(app)
        .get('/api/roles')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });

    test('should deny unauthenticated access', async () => {
      const response = await request(app)
        .get('/api/roles');

      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/roles', () => {
    test('should allow admin to create role', async () => {
      const newRole = {
        name: 'test-role',
        description: 'Test Role',
        permissions: {
          test: ['read']
        }
      };

      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(newRole);

      expect(response.status).toBe(201);
      expect(response.body.name).toBe(newRole.name);
      expect(response.body.description).toBe(newRole.description);
    });

    test('should deny regular user from creating role', async () => {
      const newRole = {
        name: 'unauthorized-role',
        description: 'Unauthorized Role',
        permissions: {
          test: ['read']
        }
      };

      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${userToken}`)
        .send(newRole);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Access denied');
    });

    test('should validate required fields', async () => {
      const invalidRole = {
        description: 'Role without name'
      };

      const response = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidRole);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Name and permissions are required');
    });
  });

  describe('PUT /api/roles/:id', () => {
    let roleId;

    beforeEach(async () => {
      // Create a test role
      const createResponse = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'update-test-role',
          description: 'Role for update testing',
          permissions: { test: ['read'] }
        });
      roleId = createResponse.body.id;
    });

    test('should allow admin to update role', async () => {
      const updateData = {
        description: 'Updated description',
        permissions: { test: ['read', 'write'] }
      };

      const response = await request(app)
        .put(`/api/roles/${roleId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.description).toBe(updateData.description);
    });

    test('should deny regular user from updating role', async () => {
      const updateData = {
        description: 'Unauthorized update'
      };

      const response = await request(app)
        .put(`/api/roles/${roleId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateData);

      expect(response.status).toBe(403);
    });
  });

  describe('DELETE /api/roles/:id', () => {
    let roleId;

    beforeEach(async () => {
      // Create a test role
      const createResponse = await request(app)
        .post('/api/roles')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          name: 'delete-test-role',
          description: 'Role for delete testing',
          permissions: { test: ['read'] }
        });
      roleId = createResponse.body.id;
    });

    test('should allow admin to delete role', async () => {
      const response = await request(app)
        .delete(`/api/roles/${roleId}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('should deny regular user from deleting role', async () => {
      const response = await request(app)
        .delete(`/api/roles/${roleId}`)
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
    });
  });

  describe('GET /api/roles/permissions', () => {
    test('should allow admin to fetch permissions', async () => {
      const response = await request(app)
        .get('/api/roles/permissions')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);
    });

    test('should deny regular user access to permissions', async () => {
      const response = await request(app)
        .get('/api/roles/permissions')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
    });
  });
});
